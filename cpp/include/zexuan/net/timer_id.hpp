/**
 * @file timer_id.hpp
 * @brief 定时器ID类，用于取消定时器
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef ZEXUAN_NET_TIMER_ID_HPP
#define ZEXUAN_NET_TIMER_ID_HPP

#include <cstdint>

namespace zexuan {
  namespace net {

    class Timer;

    /**
     * @brief 定时器ID，用于标识和取消定时器
     */
    class TimerId {
    public:
      /**
       * @brief 默认构造函数
       */
      TimerId() : timer_(nullptr), sequence_(0) {}

      /**
       * @brief 构造函数
       * @param timer 定时器指针
       * @param seq 序列号
       */
      TimerId(Timer* timer, int64_t seq) : timer_(timer), sequence_(seq) {}

      // 默认拷贝构造、析构和赋值操作符

      /**
       * @brief 检查TimerId是否有效
       */
      bool valid() const { return timer_ != nullptr; }

      friend class TimerQueue;

    private:
      Timer* timer_;
      int64_t sequence_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_TIMER_ID_HPP
