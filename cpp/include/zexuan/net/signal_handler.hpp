#ifndef ZEXUAN_NET_SIGNAL_HANDLER_HPP
#define ZEXUAN_NET_SIGNAL_HANDLER_HPP

#include <functional>
#include <map>
#include <memory>
#include <signal.h>

namespace zexuan {
  namespace net {

    class EventLoop;
    class Channel;

    class SignalHandler {
    public:

      explicit SignalHandler(EventLoop* loop);
      ~SignalHandler();

      // 禁用拷贝构造和赋值
      SignalHandler(const SignalHandler&) = delete;
      SignalHandler& operator=(const SignalHandler&) = delete;

      // 信号注册接口
      void registerSignal(int signal, SignalCallback cb);
      void unregisterSignal(int signal);

      // 便捷接口
      void registerShutdownSignals(SignalCallback cb);
      void registerUserSignals(SignalCallback cb);

    private:
      void handleRead();
      void updateSignalMask();

      EventLoop* loop_;
      int signalfd_;
      std::unique_ptr<Channel> signalChannel_;
      std::map<int, SignalCallback> signalCallbacks_;
      sigset_t signalMask_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_SIGNAL_HANDLER_HPP
