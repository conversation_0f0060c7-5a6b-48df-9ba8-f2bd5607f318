/**
 * @file timer_queue.hpp
 * @brief 现代化的定时器队列类
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef ZEXUAN_NET_TIMER_QUEUE_HPP
#define ZEXUAN_NET_TIMER_QUEUE_HPP

#include <chrono>
#include <functional>
#include <memory>
#include <set>
#include <vector>

#include "zexuan/net/callbacks.hpp"
#include "zexuan/net/timer.hpp"
#include "zexuan/net/timer_id.hpp"

namespace zexuan {
  namespace net {

    class EventLoop;
    class Channel;

    /**
     * @brief 定时器队列，管理所有定时器
     * 
     * 使用 timerfd 实现，所有定时器事件都在事件循环中处理
     * 线程安全，可以从任意线程添加/取消定时器
     */
    class TimerQueue {
    public:

      /**
       * @brief 构造函数
       * @param loop 事件循环指针
       */
      explicit TimerQueue(EventLoop* loop);

      /**
       * @brief 析构函数
       */
      ~TimerQueue();

      // 禁用拷贝构造和赋值
      TimerQueue(const TimerQueue&) = delete;
      TimerQueue& operator=(const TimerQueue&) = delete;

      /**
       * @brief 添加定时器
       * @param cb 回调函数
       * @param when 到期时间
       * @param interval 重复间隔（秒），0表示不重复
       * @return TimerId 用于取消定时器
       * 
       * 线程安全，可以从任意线程调用
       */
      TimerId addTimer(TimerCallback cb, Timestamp when, double interval);

      /**
       * @brief 取消定时器
       * @param timerId 定时器ID
       * 
       * 线程安全，可以从任意线程调用
       */
      void cancel(TimerId timerId);

    private:
      // 使用 unique_ptr 管理 Timer，避免内存泄漏
      using TimerPtr = std::unique_ptr<Timer>;
      using Entry = std::pair<Timestamp, Timer*>;
      using TimerList = std::set<Entry>;
      using ActiveTimer = std::pair<Timer*, int64_t>;
      using ActiveTimerSet = std::set<ActiveTimer>;

      /**
       * @brief 在事件循环中添加定时器
       */
      void addTimerInLoop(Timer* timer);

      /**
       * @brief 在事件循环中取消定时器
       */
      void cancelInLoop(TimerId timerId);

      /**
       * @brief 处理 timerfd 可读事件
       */
      void handleRead();

      /**
       * @brief 获取所有过期的定时器
       */
      std::vector<Entry> getExpired(Timestamp now);

      /**
       * @brief 重置过期的定时器（处理重复定时器）
       */
      void reset(const std::vector<Entry>& expired, Timestamp now);

      /**
       * @brief 插入定时器到队列中
       * @return 是否改变了最早的到期时间
       */
      bool insert(Timer* timer);

      EventLoop* loop_;
      const int timerfd_;
      std::unique_ptr<Channel> timerfdChannel_;

      // 按到期时间排序的定时器列表
      TimerList timers_;
      
      // 用于取消操作的活跃定时器集合
      ActiveTimerSet activeTimers_;
      
      // 正在处理过期定时器的标志
      bool callingExpiredTimers_;
      
      // 正在取消的定时器集合
      ActiveTimerSet cancelingTimers_;

      // 存储所有定时器的智能指针，确保内存管理
      std::vector<TimerPtr> timerStorage_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_TIMER_QUEUE_HPP
