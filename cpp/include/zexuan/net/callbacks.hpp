#ifndef ZEXUAN_NET_CALLBACKS_HPP
#define ZEXUAN_NET_CALLBACKS_HPP

#include <chrono>
#include <functional>
#include <memory>
#include <type_traits>

namespace zexuan {
  namespace net {
    class Channel;
    class Buffer;
    class TcpConnection;
    class UdsConnection;
    class EventLoop;

    template <typename T> inline T* get_pointer(const std::shared_ptr<T>& ptr) { return ptr.get(); }

    template <typename T> inline T* get_pointer(const std::unique_ptr<T>& ptr) { return ptr.get(); }

    // All client visible callbacks go here.

    using TcpConnectionPtr = std::shared_ptr<TcpConnection>;
    using UdsConnectionPtr = std::shared_ptr<UdsConnection>;
    using ChannelList = std::vector<Channel*>;
    using Timestamp = std::chrono::system_clock::time_point;

    using ConnectionCallback = std::function<void(const TcpConnectionPtr&)>;
    using CloseCallback = std::function<void(const TcpConnectionPtr&)>;
    using WriteCompleteCallback = std::function<void(const TcpConnectionPtr&)>;
    using HighWaterMarkCallback = std::function<void(const TcpConnectionPtr&, size_t)>;

    // UDS specific callbacks
    using UdsConnectionCallback = std::function<void(const UdsConnectionPtr&)>;
    using UdsCloseCallback = std::function<void(const UdsConnectionPtr&)>;
    using UdsWriteCompleteCallback = std::function<void(const UdsConnectionPtr&)>;
    using UdsHighWaterMarkCallback = std::function<void(const UdsConnectionPtr&, size_t)>;
    using UdsMessageCallback = std::function<void(const UdsConnectionPtr&, Buffer*, Timestamp)>;
    using MessageCallback = std::function<void(const TcpConnectionPtr&, Buffer*, Timestamp)>;
    using EventCallback = std::function<void()>;
    using ReadEventCallback = std::function<void(Timestamp)>;
    using NewConnectionCallback = std::function<void(int sockfd)>;
    using ThreadInitCallback = std::function<void(EventLoop*)>;
    using TimerCallback = std::function<void()>;
    using SignalCallback = std::function<void(int signal)>;
    using Functor = std::function<void()>;
    // the data has been read to (buf, len)

    void defaultConnectionCallback(const TcpConnectionPtr& conn);
    void defaultMessageCallback(const TcpConnectionPtr& conn, Buffer* buffer,
                                std::chrono::system_clock::time_point receiveTime);

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_CALLBACKS_HPP
