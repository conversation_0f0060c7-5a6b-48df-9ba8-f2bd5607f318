/**
 * @file timer.hpp
 * @brief 现代化的定时器类
 * <AUTHOR>
 * @date 2025-08-25
 */

#ifndef ZEXUAN_NET_TIMER_HPP
#define ZEXUAN_NET_TIMER_HPP

#include <atomic>
#include <chrono>
#include <functional>

#include "zexuan/net/callbacks.hpp"

namespace zexuan {
  namespace net {

    /**
     * @brief 定时器类，用于管理单个定时器事件
     */
    class Timer {
    public:
      using TimerCallback = std::function<void()>;
      using Timestamp = std::chrono::system_clock::time_point;

      /**
       * @brief 构造函数
       * @param cb 定时器回调函数
       * @param when 到期时间
       * @param interval 重复间隔（秒），0表示不重复
       */
      Timer(TimerCallback cb, Timestamp when, double interval)
          : callback_(std::move(cb)),
            expiration_(when),
            interval_(std::chrono::duration<double>(interval)),
            repeat_(interval > 0.0),
            sequence_(s_numCreated_.fetch_add(1, std::memory_order_relaxed)) {}

      // 禁用拷贝构造和赋值
      Timer(const Timer&) = delete;
      Timer& operator=(const Timer&) = delete;

      /**
       * @brief 执行定时器回调
       */
      void run() const { callback_(); }

      /**
       * @brief 获取到期时间
       */
      Timestamp expiration() const { return expiration_; }

      /**
       * @brief 是否重复执行
       */
      bool repeat() const { return repeat_; }

      /**
       * @brief 获取序列号
       */
      int64_t sequence() const { return sequence_; }

      /**
       * @brief 重启定时器（用于重复定时器）
       * @param now 当前时间
       */
      void restart(Timestamp now);

      /**
       * @brief 获取已创建的定时器数量
       */
      static int64_t numCreated() { 
        return s_numCreated_.load(std::memory_order_relaxed); 
      }

    private:
      const TimerCallback callback_;
      Timestamp expiration_;
      const std::chrono::duration<double> interval_;
      const bool repeat_;
      const int64_t sequence_;

      static std::atomic<int64_t> s_numCreated_;
    };

  }  // namespace net
}  // namespace zexuan

#endif  // ZEXUAN_NET_TIMER_HPP
