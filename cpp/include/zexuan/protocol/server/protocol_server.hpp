#pragma once

#include <spdlog/spdlog.h>

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <nlohmann/json.hpp>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

// 引入现有的网络库
#include "zexuan/net/address.hpp"
#include "zexuan/net/buffer.hpp"
#include "zexuan/net/callbacks.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/net/tcp_connection.hpp"
#include "zexuan/net/tcp_server.hpp"

// 引入协议网关
#include "zexuan/protocol/gateway/protocol_gateway.hpp"

namespace zexuan {
  namespace protocol {
    namespace server {

      /**
       * @brief 协议服务器类 - 基于现有网络库的简单封装
       *
       * 职责：
       * 1. 管理TCP连接和对应的ProtocolGateway
       * 2. 直接回调Gateway处理协议数据
       * 3. 利用现有的网络库处理TCP连接
       */
      class ProtocolServer {
      public:
        explicit ProtocolServer(net::EventLoop* event_loop, const std::string& config_file_path);
        virtual ~ProtocolServer();

        // 生命周期管理
        bool Initialize();
        bool Start();
        bool Stop();
        void Shutdown();

        // 状态查询
        bool IsRunning() const { return is_running_.load(); }
        size_t GetConnectionCount() const;
        std::vector<std::string> GetAllConnectionNames() const;

        // 统计信息
        struct Statistics {
          std::atomic<uint64_t> total_connections{0};
          std::atomic<uint64_t> active_connections{0};
          std::atomic<uint64_t> bytes_sent{0};
          std::atomic<uint64_t> bytes_received{0};
        };

        const Statistics& GetStatistics() const { return stats_; }
        void ResetStatistics();

        /**
         * @brief 发送数据到指定连接
         * @param conn 连接对象
         * @param data 数据
         * @return 是否发送成功
         */
        bool SendData(const net::TcpConnectionPtr& conn, const std::vector<uint8_t>& data);

      private:
        // 网络回调处理
        void OnConnection(const net::TcpConnectionPtr& conn);
        void OnMessage(const net::TcpConnectionPtr& conn, net::Buffer* buffer,
                       net::Timestamp receiveTime);
        void OnWriteComplete(const net::TcpConnectionPtr& conn);

        // 连接管理（简化：直接管理TcpConnection）
        void AddConnection(const net::TcpConnectionPtr& conn);
        void RemoveConnection(const net::TcpConnectionPtr& conn);

        // Gateway管理（直接与TcpConnection绑定）
        bool CreateGatewayForConnection(const net::TcpConnectionPtr& conn);
        void DestroyGatewayForConnection(const net::TcpConnectionPtr& conn);

        // 分帧处理（直接使用TcpConnection）
        bool ExtractOneFrame(net::Buffer* buffer, const net::TcpConnectionPtr& conn);
        void SendFrameToGateway(const net::TcpConnectionPtr& conn,
                                const std::vector<uint8_t>& frame_data);

      private:
        // 网络组件
        net::EventLoop* event_loop_;  // 外部传入，不拥有所有权
        std::unique_ptr<net::TcpServer> tcp_server_;

        // 连接管理（简化：直接管理连接，不需要ID映射）
        std::unordered_set<net::TcpConnectionPtr> connections_;
        mutable std::mutex connections_mutex_;

        // Gateway管理（直接与TcpConnection绑定）
        std::unordered_map<net::TcpConnectionPtr,
                           std::shared_ptr<protocol::gateway::ProtocolGateway>>
            gateways_;
        mutable std::mutex gateways_mutex_;

        // 配置文件路径
        std::string config_file_path_;

        // 配置参数（从JSON读取）
        std::string listen_address_;
        uint16_t listen_port_;
        int max_connections_;
        int thread_pool_size_;

        // 状态
        std::atomic<bool> is_initialized_{false};
        std::atomic<bool> is_running_{false};

        // 统计信息
        Statistics stats_;
      };

    }  // namespace server
  }  // namespace protocol
}  // namespace zexuan
