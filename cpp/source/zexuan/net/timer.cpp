/**
 * @file timer.cpp
 * @brief 定时器类实现
 * <AUTHOR>
 * @date 2025-08-25
 */

#include "zexuan/net/timer.hpp"

using namespace zexuan;
using namespace zexuan::net;

std::atomic<int64_t> Timer::s_numCreated_{0};

void Timer::restart(Timestamp now) {
  if (repeat_) {
    expiration_ = now + std::chrono::duration_cast<std::chrono::system_clock::duration>(interval_);
  } else {
    expiration_ = Timestamp{};  // 无效时间戳
  }
}
