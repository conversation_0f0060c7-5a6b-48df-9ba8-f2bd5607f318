/**
 * @file subscription_manager.cpp
 * @brief 订阅管理器实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/bus/subscription_manager.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>

namespace zexuan {
namespace bus {

void SubscriptionManager::addClient(const TcpConnectionPtr& conn, const std::string& client_name) {
    if (!conn) {
        spdlog::error("Cannot add null connection");
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto subscription = std::make_shared<ClientSubscription>();
    subscription->client_name = client_name.empty() ? conn->name() : client_name;
    
    client_subscriptions_[conn] = subscription;
    
    spdlog::info("Added client: {} ({})", subscription->client_name, conn->peerAddress().toIpPort());
}

void SubscriptionManager::removeClient(const TcpConnectionPtr& conn) {
    if (!conn) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = client_subscriptions_.find(conn);
    if (it != client_subscriptions_.end()) {
        std::string client_name = it->second->client_name;
        zexuan::base::ObjectId client_id = it->second->client_id;
        
        // 清理所有订阅
        cleanupClientSubscriptions(conn);
        
        // 清理客户端ID映射
        if (client_id != zexuan::base::INVALID_ID) {
            client_id_to_connection_.erase(client_id);
        }
        
        // 移除客户端
        client_subscriptions_.erase(it);
        
        spdlog::info("Removed client: {} (ID: {}) ({})", client_name, client_id, conn->peerAddress().toIpPort());
    }
}

zexuan::base::ControlMessage SubscriptionManager::handleSubscription(const TcpConnectionPtr& conn, const zexuan::base::SubscriptionMessage& subscription_msg) {
    if (!conn) {
        zexuan::base::ControlMessage response;
        response.action = "subscribe_response";
        response.success = false;
        response.message = "Invalid connection";
        return response;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = client_subscriptions_.find(conn);
    if (it == client_subscriptions_.end()) {
        zexuan::base::ControlMessage response;
        response.action = "subscribe_response";
        response.success = false;
        response.message = "Client not found";
        return response;
    }

    auto& subscription = it->second;

    try {
        if (subscription_msg.action == zexuan::base::SubscriptionMessage::Action::SUBSCRIBE) {
            // 处理订阅
            addMessageTypeSubscriptions(conn, subscription_msg.message_types);
            addEventTypeSubscriptions(conn, subscription_msg.event_types);

            // 更新客户端ID映射
            if (subscription_msg.client_id != zexuan::base::INVALID_ID) {
                subscription->client_id = subscription_msg.client_id;
                client_id_to_connection_[subscription_msg.client_id] = conn;
                spdlog::debug("Updated client_id mapping: {} -> {}", 
                             subscription_msg.client_id, subscription->client_name);
            }

            spdlog::info("Client {} (ID: {}) subscribed to {} message types and {} event types",
                        subscription->client_name,
                        subscription->client_id,
                        subscription_msg.message_types.size(),
                        subscription_msg.event_types.size());

        } else if (subscription_msg.action == zexuan::base::SubscriptionMessage::Action::UNSUBSCRIBE) {
            // 处理取消订阅
            removeMessageTypeSubscriptions(conn, subscription_msg.message_types);
            removeEventTypeSubscriptions(conn, subscription_msg.event_types);

            spdlog::info("Client {} unsubscribed from {} message types and {} event types",
                        subscription->client_name,
                        subscription_msg.message_types.size(),
                        subscription_msg.event_types.size());
        }

        // 构建成功响应
        zexuan::base::ControlMessage response;
        response.action = "subscribe_response";
        response.success = true;
        response.message = (subscription_msg.action == zexuan::base::SubscriptionMessage::Action::SUBSCRIBE) ?
                          "Subscription successful" : "Unsubscription successful";
        
        // 返回当前订阅状态
        response.subscribed_message_types.assign(subscription->message_types.begin(), subscription->message_types.end());
        response.subscribed_event_types.assign(subscription->event_types.begin(), subscription->event_types.end());
        
        return response;
        
    } catch (const std::exception& e) {
        spdlog::error("Failed to handle subscription for client {}: {}", subscription->client_name, e.what());

        zexuan::base::ControlMessage response;
        response.action = "subscribe_response";
        response.success = false;
        response.message = "Internal error: " + std::string(e.what());
        return response;
    }
}

SubscriptionManager::ConnectionList SubscriptionManager::getSubscribersForMessageType(int message_type) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ConnectionList subscribers;
    
    auto it = message_type_subscribers_.find(message_type);
    if (it != message_type_subscribers_.end()) {
        for (const auto& conn : it->second) {
            if (conn && conn->connected()) {
                subscribers.push_back(conn);
            }
        }
    }
    
    return subscribers;
}

SubscriptionManager::ConnectionList SubscriptionManager::getSubscribersForEventType(int event_type) const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ConnectionList subscribers;
    
    auto it = event_type_subscribers_.find(event_type);
    if (it != event_type_subscribers_.end()) {
        for (const auto& conn : it->second) {
            if (conn && conn->connected()) {
                subscribers.push_back(conn);
            }
        }
    }
    
    return subscribers;
}

std::shared_ptr<ClientSubscription> SubscriptionManager::getClientSubscription(const TcpConnectionPtr& conn) const {
    if (!conn) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = client_subscriptions_.find(conn);
    return (it != client_subscriptions_.end()) ? it->second : nullptr;
}

size_t SubscriptionManager::getClientCount() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return client_subscriptions_.size();
}

SubscriptionManager::ConnectionList SubscriptionManager::getAllClients() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    ConnectionList clients;
    clients.reserve(client_subscriptions_.size());
    
    for (const auto& pair : client_subscriptions_) {
        if (pair.first && pair.first->connected()) {
            clients.push_back(pair.first);
        }
    }
    
    return clients;
}

size_t SubscriptionManager::cleanupInvalidConnections() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    size_t cleaned_count = 0;
    auto it = client_subscriptions_.begin();
    
    while (it != client_subscriptions_.end()) {
        if (!it->first || !it->first->connected()) {
            cleanupClientSubscriptions(it->first);
            it = client_subscriptions_.erase(it);
            ++cleaned_count;
        } else {
            ++it;
        }
    }
    
    if (cleaned_count > 0) {
        spdlog::info("Cleaned up {} invalid connections", cleaned_count);
    }
    
    return cleaned_count;
}

SubscriptionManager::Statistics SubscriptionManager::getStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    
    Statistics stats;
    stats.total_clients = client_subscriptions_.size();
    
    // 统计订阅数量
    for (const auto& pair : client_subscriptions_) {
        const auto& subscription = pair.second;
        stats.total_message_subscriptions += subscription->message_types.size();
        stats.total_event_subscriptions += subscription->event_types.size();
    }
    
    // 统计每种类型的订阅者数量
    for (const auto& pair : message_type_subscribers_) {
        stats.message_type_subscribers[pair.first] = pair.second.size();
    }
    
    for (const auto& pair : event_type_subscribers_) {
        stats.event_type_subscribers[pair.first] = pair.second.size();
    }
    
    return stats;
}

SubscriptionManager::TcpConnectionPtr SubscriptionManager::findConnectionByClientId(zexuan::base::ObjectId client_id) const {
    if (client_id == zexuan::base::INVALID_ID) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(mutex_);
    
    auto it = client_id_to_connection_.find(client_id);
    if (it != client_id_to_connection_.end() && it->second && it->second->connected()) {
        return it->second;
    }
    
    return nullptr;
}

void SubscriptionManager::addMessageTypeSubscriptions(const TcpConnectionPtr& conn, const std::vector<int>& message_types) {
    auto it = client_subscriptions_.find(conn);
    if (it == client_subscriptions_.end()) {
        return;
    }

    auto& subscription = it->second;

    for (int message_type : message_types) {
        // 添加到客户端订阅信息
        subscription->message_types.insert(message_type);

        // 添加到消息类型订阅者映射
        message_type_subscribers_[message_type].insert(conn);
    }
}

void SubscriptionManager::removeMessageTypeSubscriptions(const TcpConnectionPtr& conn, const std::vector<int>& message_types) {
    auto it = client_subscriptions_.find(conn);
    if (it == client_subscriptions_.end()) {
        return;
    }

    auto& subscription = it->second;

    for (int message_type : message_types) {
        // 从客户端订阅信息中移除
        subscription->message_types.erase(message_type);

        // 从消息类型订阅者映射中移除
        auto type_it = message_type_subscribers_.find(message_type);
        if (type_it != message_type_subscribers_.end()) {
            type_it->second.erase(conn);

            // 如果没有订阅者了，移除整个映射项
            if (type_it->second.empty()) {
                message_type_subscribers_.erase(type_it);
            }
        }
    }
}

void SubscriptionManager::addEventTypeSubscriptions(const TcpConnectionPtr& conn, const std::vector<int>& event_types) {
    auto it = client_subscriptions_.find(conn);
    if (it == client_subscriptions_.end()) {
        return;
    }

    auto& subscription = it->second;

    for (int event_type : event_types) {
        // 添加到客户端订阅信息
        subscription->event_types.insert(event_type);

        // 添加到事件类型订阅者映射
        event_type_subscribers_[event_type].insert(conn);
    }
}

void SubscriptionManager::removeEventTypeSubscriptions(const TcpConnectionPtr& conn, const std::vector<int>& event_types) {
    auto it = client_subscriptions_.find(conn);
    if (it == client_subscriptions_.end()) {
        return;
    }

    auto& subscription = it->second;

    for (int event_type : event_types) {
        // 从客户端订阅信息中移除
        subscription->event_types.erase(event_type);

        // 从事件类型订阅者映射中移除
        auto type_it = event_type_subscribers_.find(event_type);
        if (type_it != event_type_subscribers_.end()) {
            type_it->second.erase(conn);

            // 如果没有订阅者了，移除整个映射项
            if (type_it->second.empty()) {
                event_type_subscribers_.erase(type_it);
            }
        }
    }
}

void SubscriptionManager::cleanupClientSubscriptions(const TcpConnectionPtr& conn) {
    auto it = client_subscriptions_.find(conn);
    if (it == client_subscriptions_.end()) {
        return;
    }

    const auto& subscription = it->second;

    // 清理客户端ID映射
    if (subscription->client_id != zexuan::base::INVALID_ID) {
        client_id_to_connection_.erase(subscription->client_id);
    }

    // 清理消息类型订阅
    for (int message_type : subscription->message_types) {
        auto type_it = message_type_subscribers_.find(message_type);
        if (type_it != message_type_subscribers_.end()) {
            type_it->second.erase(conn);
            if (type_it->second.empty()) {
                message_type_subscribers_.erase(type_it);
            }
        }
    }

    // 清理事件类型订阅
    for (int event_type : subscription->event_types) {
        auto type_it = event_type_subscribers_.find(event_type);
        if (type_it != event_type_subscribers_.end()) {
            type_it->second.erase(conn);
            if (type_it->second.empty()) {
                event_type_subscribers_.erase(type_it);
            }
        }
    }
}

} // namespace bus
} // namespace zexuan
