/**
 * @file message_serializer.cpp
 * @brief JSON消息序列化器实现
 * <AUTHOR>
 * @date 2025-08-24
 */

#include "zexuan/utils/message_serializer.hpp"
#include <spdlog/spdlog.h>
#include <algorithm>
#include <array>

namespace zexuan {
namespace utils {

std::string MessageSerializer::serializeCommonMessage(const zexuan::base::CommonMessage& msg) {
    try {
        nlohmann::json json;
        json["msg_category"] = "common";
        json["type"] = static_cast<int>(msg.type);
        json["source_id"] = msg.source_id;
        json["target_id"] = msg.target_id;
        json["invoke_id"] = msg.invoke_id;
        json["data"] = encodeBase64(msg.data);
        json["is_last_msg"] = msg.b_lastmsg;
        
        return json.dump();
    } catch (const std::exception& e) {
        spdlog::error("Failed to serialize CommonMessage: {}", e.what());
        return "";
    }
}

std::optional<zexuan::base::CommonMessage> MessageSerializer::deserializeCommonMessage(const std::string& json_str) {
    auto json_opt = safeParseJson(json_str);
    if (!json_opt) {
        return std::nullopt;
    }
    
    auto json = json_opt.value();
    
    // 验证必需字段
    std::vector<std::string> required_fields = {"msg_category", "type", "source_id", "target_id", "invoke_id", "data", "is_last_msg"};
    if (!validateRequiredFields(json, required_fields)) {
        spdlog::error("CommonMessage missing required fields");
        return std::nullopt;
    }
    
    // 验证消息类别
    if (json["msg_category"] != "common") {
        spdlog::error("Invalid msg_category for CommonMessage: {}", json["msg_category"].get<std::string>());
        return std::nullopt;
    }
    
    try {
        zexuan::base::CommonMessage msg;
        msg.type = static_cast<zexuan::base::MessageType>(json["type"].get<int>());
        msg.source_id = json["source_id"].get<zexuan::base::ObjectId>();
        msg.target_id = json["target_id"].get<zexuan::base::ObjectId>();
        msg.invoke_id = json["invoke_id"].get<std::string>();
        msg.data = decodeBase64(json["data"].get<std::string>());
        msg.b_lastmsg = json["is_last_msg"].get<bool>();
        
        return msg;
    } catch (const std::exception& e) {
        spdlog::error("Failed to deserialize CommonMessage: {}", e.what());
        return std::nullopt;
    }
}

std::string MessageSerializer::serializeEventMessage(const zexuan::base::EventMessage& msg) {
    try {
        nlohmann::json json;
        json["msg_category"] = "event";
        json["event_type"] = msg.event_type;
        
        // 序列化DeviceUUID
        nlohmann::json device_uuid_json;
        device_uuid_json["device_id"] = msg.device_uuid.device_id;
        device_uuid_json["category"] = msg.device_uuid.category;
        json["device_uuid"] = device_uuid_json;
        
        json["source_id"] = msg.source_id;
        json["description"] = msg.description;
        json["data"] = encodeBase64(msg.data);
        
        return json.dump();
    } catch (const std::exception& e) {
        spdlog::error("Failed to serialize EventMessage: {}", e.what());
        return "";
    }
}

std::optional<zexuan::base::EventMessage> MessageSerializer::deserializeEventMessage(const std::string& json_str) {
    auto json_opt = safeParseJson(json_str);
    if (!json_opt) {
        return std::nullopt;
    }
    
    auto json = json_opt.value();
    
    // 验证必需字段
    std::vector<std::string> required_fields = {"msg_category", "event_type", "device_uuid", "source_id", "description", "data"};
    if (!validateRequiredFields(json, required_fields)) {
        spdlog::error("EventMessage missing required fields");
        return std::nullopt;
    }
    
    // 验证消息类别
    if (json["msg_category"] != "event") {
        spdlog::error("Invalid msg_category for EventMessage: {}", json["msg_category"].get<std::string>());
        return std::nullopt;
    }
    
    try {
        zexuan::base::EventMessage msg;
        msg.event_type = json["event_type"].get<zexuan::base::EventType>();
        
        // 反序列化DeviceUUID
        auto device_uuid_json = json["device_uuid"];
        msg.device_uuid.device_id = device_uuid_json["device_id"].get<uint32_t>();
        msg.device_uuid.category = static_cast<zexuan::base::DeviceCategory>(device_uuid_json["category"].get<uint8_t>());
        
        msg.source_id = json["source_id"].get<zexuan::base::ObjectId>();
        msg.description = json["description"].get<std::string>();
        msg.data = decodeBase64(json["data"].get<std::string>());
        
        return msg;
    } catch (const std::exception& e) {
        spdlog::error("Failed to deserialize EventMessage: {}", e.what());
        return std::nullopt;
    }
}

std::string MessageSerializer::serializeSubscriptionMessage(const zexuan::base::SubscriptionMessage& msg) {
    try {
        nlohmann::json json;
        json["action"] = (msg.action == zexuan::base::SubscriptionMessage::Action::SUBSCRIBE) ? "subscribe" : "unsubscribe";
        json["message_types"] = msg.message_types;
        json["event_types"] = msg.event_types;
        json["client_id"] = msg.client_id;
        
        return json.dump();
    } catch (const std::exception& e) {
        spdlog::error("Failed to serialize SubscriptionMessage: {}", e.what());
        return "";
    }
}

std::optional<zexuan::base::SubscriptionMessage> MessageSerializer::deserializeSubscriptionMessage(const std::string& json_str) {
    auto json_opt = safeParseJson(json_str);
    if (!json_opt) {
        return std::nullopt;
    }
    
    auto json = json_opt.value();
    
    // 验证必需字段
    std::vector<std::string> required_fields = {"action"};
    if (!validateRequiredFields(json, required_fields)) {
        spdlog::error("SubscriptionMessage missing required fields");
        return std::nullopt;
    }
    
    try {
        zexuan::base::SubscriptionMessage msg;
        
        std::string action_str = json["action"].get<std::string>();
        if (action_str == "subscribe") {
            msg.action = zexuan::base::SubscriptionMessage::Action::SUBSCRIBE;
        } else if (action_str == "unsubscribe") {
            msg.action = zexuan::base::SubscriptionMessage::Action::UNSUBSCRIBE;
        } else {
            spdlog::error("Invalid action for SubscriptionMessage: {}", action_str);
            return std::nullopt;
        }
        
        if (json.contains("message_types") && json["message_types"].is_array()) {
            msg.message_types = json["message_types"].get<std::vector<int>>();
        }
        
        if (json.contains("event_types") && json["event_types"].is_array()) {
            msg.event_types = json["event_types"].get<std::vector<int>>();
        }
        
        if (json.contains("client_id") && json["client_id"].is_number_integer()) {
            msg.client_id = json["client_id"].get<zexuan::base::ObjectId>();
        }
        
        return msg;
    } catch (const std::exception& e) {
        spdlog::error("Failed to deserialize SubscriptionMessage: {}", e.what());
        return std::nullopt;
    }
}

std::string MessageSerializer::serializeControlMessage(const zexuan::base::ControlMessage& msg) {
    try {
        nlohmann::json json;
        json["msg_category"] = "control";
        json["action"] = msg.action;
        json["success"] = msg.success;
        json["message"] = msg.message;
        json["subscribed_message_types"] = msg.subscribed_message_types;
        json["subscribed_event_types"] = msg.subscribed_event_types;
        
        return json.dump();
    } catch (const std::exception& e) {
        spdlog::error("Failed to serialize ControlMessage: {}", e.what());
        return "";
    }
}

std::optional<zexuan::base::ControlMessage> MessageSerializer::deserializeControlMessage(const std::string& json_str) {
    auto json_opt = safeParseJson(json_str);
    if (!json_opt) {
        return std::nullopt;
    }
    
    auto json = json_opt.value();
    
    // 验证必需字段
    std::vector<std::string> required_fields = {"msg_category", "action", "success", "message"};
    if (!validateRequiredFields(json, required_fields)) {
        spdlog::error("ControlMessage missing required fields");
        return std::nullopt;
    }
    
    // 验证消息类别
    if (json["msg_category"] != "control") {
        spdlog::error("Invalid msg_category for ControlMessage: {}", json["msg_category"].get<std::string>());
        return std::nullopt;
    }
    
    try {
        zexuan::base::ControlMessage msg;
        msg.action = json["action"].get<std::string>();
        msg.success = json["success"].get<bool>();
        msg.message = json["message"].get<std::string>();
        
        if (json.contains("subscribed_message_types") && json["subscribed_message_types"].is_array()) {
            msg.subscribed_message_types = json["subscribed_message_types"].get<std::vector<int>>();
        }
        
        if (json.contains("subscribed_event_types") && json["subscribed_event_types"].is_array()) {
            msg.subscribed_event_types = json["subscribed_event_types"].get<std::vector<int>>();
        }
        
        return msg;
    } catch (const std::exception& e) {
        spdlog::error("Failed to deserialize ControlMessage: {}", e.what());
        return std::nullopt;
    }
}

std::string MessageSerializer::encodeBase64(const std::vector<uint8_t>& data) {
    if (data.empty()) {
        return "";
    }

    static const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    std::string result;

    size_t i = 0;
    uint8_t char_array_3[3];
    uint8_t char_array_4[4];

    for (uint8_t byte : data) {
        char_array_3[i++] = byte;
        if (i == 3) {
            char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
            char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
            char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
            char_array_4[3] = char_array_3[2] & 0x3f;

            for (i = 0; i < 4; i++) {
                result += chars[char_array_4[i]];
            }
            i = 0;
        }
    }

    if (i) {
        for (size_t j = i; j < 3; j++) {
            char_array_3[j] = '\0';
        }

        char_array_4[0] = (char_array_3[0] & 0xfc) >> 2;
        char_array_4[1] = ((char_array_3[0] & 0x03) << 4) + ((char_array_3[1] & 0xf0) >> 4);
        char_array_4[2] = ((char_array_3[1] & 0x0f) << 2) + ((char_array_3[2] & 0xc0) >> 6);
        char_array_4[3] = char_array_3[2] & 0x3f;

        for (size_t j = 0; j < i + 1; j++) {
            result += chars[char_array_4[j]];
        }

        while (i++ < 3) {
            result += '=';
        }
    }

    return result;
}

std::vector<uint8_t> MessageSerializer::decodeBase64(const std::string& encoded) {
    if (encoded.empty()) {
        return {};
    }

    static const std::array<int, 128> decode_table = []() {
        std::array<int, 128> table{};
        std::fill(table.begin(), table.end(), -1);

        for (int i = 0; i < 26; i++) {
            table['A' + i] = i;
            table['a' + i] = i + 26;
        }
        for (int i = 0; i < 10; i++) {
            table['0' + i] = i + 52;
        }
        table['+'] = 62;
        table['/'] = 63;
        return table;
    }();

    std::vector<uint8_t> result;
    size_t i = 0;
    uint8_t char_array_4[4], char_array_3[3];

    for (char c : encoded) {
        if (c == '=') break;
        if (static_cast<unsigned char>(c) >= 128 || decode_table[static_cast<unsigned char>(c)] == -1) continue;

        char_array_4[i++] = decode_table[static_cast<unsigned char>(c)];
        if (i == 4) {
            char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
            char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
            char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

            for (i = 0; i < 3; i++) {
                result.push_back(char_array_3[i]);
            }
            i = 0;
        }
    }

    if (i) {
        for (size_t j = i; j < 4; j++) {
            char_array_4[j] = 0;
        }

        char_array_3[0] = (char_array_4[0] << 2) + ((char_array_4[1] & 0x30) >> 4);
        char_array_3[1] = ((char_array_4[1] & 0xf) << 4) + ((char_array_4[2] & 0x3c) >> 2);
        char_array_3[2] = ((char_array_4[2] & 0x3) << 6) + char_array_4[3];

        for (size_t j = 0; j < i - 1; j++) {
            result.push_back(char_array_3[j]);
        }
    }

    return result;
}

std::optional<nlohmann::json> MessageSerializer::safeParseJson(const std::string& json_str) {
    try {
        return nlohmann::json::parse(json_str);
    } catch (const std::exception& e) {
        spdlog::error("Failed to parse JSON: {}", e.what());
        return std::nullopt;
    }
}

bool MessageSerializer::validateRequiredFields(const nlohmann::json& json, const std::vector<std::string>& required_fields) {
    for (const auto& field : required_fields) {
        if (!json.contains(field)) {
            spdlog::error("Missing required field: {}", field);
            return false;
        }
    }
    return true;
}

} // namespace utils
} // namespace zexuan
