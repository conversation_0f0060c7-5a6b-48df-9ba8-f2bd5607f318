#include <spdlog/spdlog.h>

#include <atomic>
#include <chrono>
#include <iostream>
#include <memory>
#include <thread>

#include "zexuan/base/logger_manager.hpp"
#include "zexuan/net/event_loop.hpp"
#include "zexuan/protocol/server/protocol_server.hpp"

using namespace zexuan::protocol::server;
using namespace zexuan::base;

// 全局变量，用于信号处理
std::shared_ptr<ProtocolServer> g_server = nullptr;
std::atomic<bool> g_running{true};

// 信号处理函数
void signalHandler(int signal) {
  // 在信号处理函数中只设置标志，不直接操作复杂对象
  g_running.store(false);

  // 只调用Stop()让EventLoop退出，不调用Shutdown()
  if (g_server) {
    g_server->Stop();
  }
}

int main(int argc, char* argv[]) {
  // 初始化日志系统
  std::string config_path = "./config/config.json";
  if (argc > 1) {
    config_path = argv[1];
  }

  if (!LoggerManager::initialize("protocol_server.log", config_path)) {
    std::cerr << "Failed to initialize logger" << std::endl;
    return -1;
  }

  spdlog::info("Protocol Test Server starting...");

  // 注册信号处理
  signal(SIGINT, signalHandler);
  signal(SIGTERM, signalHandler);

  try {
    spdlog::info("Using config file: {}", config_path);
    g_server = std::make_shared<ProtocolServer>(config_path);

    // 初始化服务器
    if (!g_server->Initialize()) {
      spdlog::error("Failed to initialize protocol server");
      return -1;
    }

    spdlog::info("Protocol server initialized successfully");
    spdlog::info("Server is ready to start");
    spdlog::info("Press Ctrl+C to stop the server");

    // 启动服务器（这会阻塞直到服务器停止）
    if (!g_server->Start()) {
      spdlog::error("Failed to start protocol server");
      return -1;
    }

    spdlog::info("Protocol server has stopped");

    // 确保完全清理服务器，等待所有工作线程结束
    g_server->Shutdown();
    g_server.reset();

  } catch (const std::exception& e) {
    spdlog::error("Exception in main: {}", e.what());

    // 异常情况下也要清理服务器
    if (g_server) {
      g_server->Shutdown();
      g_server.reset();
    }
    return -1;
  }

  return 0;
}