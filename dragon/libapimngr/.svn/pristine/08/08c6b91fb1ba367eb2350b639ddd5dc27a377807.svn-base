/*
 * Brief: 六角图动态库接口调用
 * Created on: 2024-08-20
 * Author: zk
 *
 */

#ifndef _LIBMNGR_HEXAGONAL_DIAGRAM_ANALYZE_H_
#define _LIBMNGR_HEXAGONAL_DIAGRAM_ANALYZE_H_

#include "ZxLib.h"
#include "ZxMessageLog.h"

#include <string>
#include <vector>
#include <cstring>
#include <map>
#include <list>
using namespace std;

/* @brief   一次设备装置类型 */
// 不使用枚举仅作展示，参照"tb_dev_index_base"的"dev_type"字段
enum eDeviceType
{
	eDeviceTypeNull = 0,
	eBus            = 1,      // 母线
	eLine           = 2,      // 线路
	eBreaker        = 3,      // 开关
	eGenerator      = 4,      // 发电机
	eReactor        = 5,      // 电抗器
	eTransformer    = 6,      // 变压器
	eSwitch         = 7,      // 刀闸
	eCapacitor      = 8,      // 电容器
	eStation        = 9,      // 厂站
	eSecdev         = 10,     // 二次设备
};

/* @brief   变压器按侧分类 */
enum eTransSide
{
	eTransSideNull = 0,
	eHigh_Trans    = 1,       // 高侧，线路用高侧
	eMedium_Trans  = 2,       // 中侧
	eLow_Trans     = 3,       // 低侧
};

/* @brief   定值项功能类型定义 */
enum eSgFunType
{
	eSgFunTypeNull = 0,
	eSettingCT_1   = 1,       // CT一次额定值
	eSettingCT_2   = 2,       // CT二次额定值
	eSettingCT     = 3,       // CT变比
	eSettingPT_1   = 4,       // PT一次额定值
};

/* @brief   测控功能类型定义 */
enum eCkFunType
{
	eCkFunTypeNull = 0,
	eVoltageA      = 1,       // A相电压幅值-测控
	eVoltageB      = 2,       // B相电压幅值-测控
	eVoltageC      = 3,       // C相电压幅值-测控
	ePhaseVoA      = 4,       // A相电压相角-测控
	ePhaseVoB      = 5,       // B相电压相角-测控
	ePhaseVoC      = 6,       // C相电压相角-测控
	eCurrentA      = 7,       // A相电流幅值-测控
	eCurrentB      = 8,       // B相电流幅值-测控
	eCurrentC      = 9,       // C相电流幅值-测控
	ePhaseCuA      = 10,      // A相电流相角-测控
	ePhaseCuB      = 11,      // B相电流相角-测控
	ePhaseCuC      = 12,      // C相电流相角-测控
	eVoltageTYD    = 13,      // TYD同期电压-测控
	ePowerP_dui    = 14,      // 对侧有功-测控
	ePowerQ_dui    = 15,      // 对侧无功-测控
};

/* @brief   分析判据类型定义 */
enum eAnalyzeType
{
	eTotalResult          = 1,     // 分析结果-总结论
	eCurrentLoopResult    = 2,     // 分析结果-电流回路异常
	eDiffCurrentResult    = 3,     // 分析结果-差流异常
	eCurrentBalanceResult = 4,     // 分析结果-三相电流不平衡
	ePhaseAngleResult     = 5,     // 分析结果-相角异常
	// 测控数据比对结果
	eCkTransRatioResult   = 6,     // 分析结果-变比异常
	eCkPowerDirResult     = 7,     // 分析结果-功率方向异常
	eCkPowerValueResult   = 8,     // 分析结果-功率大小异常
	eCkPowerBalanceResult = 9,     // 分析结果-功率平衡
};

/* @brief   计算结果类型定义 */
enum eCalcType
{
	eCalcPH    = 1,      // 高压侧有功负荷/线路有功负荷-保信
	eCalcPM    = 2,      // 中压侧有功负荷-保信
	eCalcPL    = 3,      // 低压侧有功负荷-保信
	eCalcQH    = 4,      // 高压侧无功负荷/线路无功负荷-保信
	eCalcQM    = 5,      // 中压侧无功负荷-保信
	eCalcQL    = 6,      // 低压侧无功负荷-保信
	eCalcSH    = 7,      // 高压侧功率因数/线路功率因数-保信
	eCalcSM    = 8,      // 中压侧功率因数-保信
	eCalcSL    = 9,      // 低压侧功率因数-保信
	eCalcAngSH = 10,     // 高压侧功率因数角/线路功率因数角-保信
	eCalcAngSM = 11,     // 中压侧功率因数角-保信
	eCalcAngSL = 12,     // 低压侧功率因数角-保信
};

/* @brief   分析结果-总结论 */
enum eAnalyzeResult
{
	HEXDIAG_ANALYZE_RESULT_UN_ANALYZE  = -1, // 测试条件不具备，未分析
	HEXDIAG_ANALYZE_RESULT_NORMAL      =  0, // 分析结果正常
	HEXDIAG_ANALYZE_RESULT_ABNORMAL    =  1, // 分析结果异常
};

/* @brief   分析结果-电流回路异常 */
enum eAnalyzeResultCurrentLoop
{
	CURRENT_LOOP_UN_ANALYZE  = -1, // 未比对
	CURRENT_LOOP_NORMAL      =  0, // 正常
	CURRENT_LOOP_ABNORMAL    =  1, // 异常
};

/* @brief   分析结果-差流异常 */
enum eAnalyzeResultDiffCurrent
{
	DIFF_CURRENT_UN_ANALYZE  = -1, // 未比对
	DIFF_CURRENT_NORMAL      =  0, // 正常
	DIFF_CURRENT_ABNORMAL    =  1, // 异常
};

/* @brief   分析结果-三相电流不平衡 */
enum eAnalyzeResultCurrentBalance
{
	CURRENT_BALANCE_UN_ANALYZE  = -1, // 未比对
	CURRENT_BALANCE_NORMAL      =  0, // 平衡
	CURRENT_BALANCE_ABNORMAL    =  1, // 不平衡
};

/* @brief   分析结果-相角异常 */
enum eAnalyzeResultPhaseAngle
{
	PHASE_ANGLE_UN_ANALYZE  = -1, // 未比对
	PHASE_ANGLE_NORMAL      =  0, // 正常
	PHASE_ANGLE_ABNORMAL    =  1, // 异常
};

/* @brief   分析结果-变比异常 */
enum eAnalyzeResultCkTransRatio
{
	CK_TRANS_RATIO_UN_ANALYZE  = -1, // 未比对
	CK_TRANS_RATIO_NORMAL      =  0, // 正常
	CK_TRANS_RATIO_ABNORMAL    =  1, // 异常
};

/* @brief   分析结果-功率方向异常 */
enum eAnalyzeResultCkPowerDir
{
	CK_POWER_DIR_UN_ANALYZE  = -1, // 未比对
	CK_POWER_DIR_NORMAL      =  0, // 正常
	CK_POWER_DIR_ABNORMAL    =  1, // 异常
};

/* @brief   分析结果-功率大小异常 */
enum eAnalyzeResultCkPowerValue
{
	CK_POWER_VALUE_UN_ANALYZE  = -1, // 未比对
	CK_POWER_VALUE_NORMAL      =  0, // 正常
	CK_POWER_VALUE_ABNORMAL    =  1, // 异常
};

/* @brief   分析结果-功率平衡 */
enum eAnalyzeResultCkPowerBalance
{
	CK_POWER_BALANCE_UN_ANALYZE  = -1, // 未比对
	CK_POWER_BALANCE_NORMAL      =  0, // 平衡
	CK_POWER_BALANCE_ABNORMAL    =  1, // 不平衡
};

/* @brief   输出-判据分析结果 */
typedef struct _HD_Ana_Output
{
	string strAnaName;        // 判据名称
	string strAnaReason;      // 结论原因
	int nAnalyzeType;         // 分析判据类型
	int nAnaValue;            // 判据分析结果 -1-未必对 0-正常 1-异常

	_HD_Ana_Output()
	{
		strAnaName = "";
		strAnaReason = "";
		nAnalyzeType = 0; // 枚举eAnalyzeType列举了各分析判据类型
		nAnaValue = -1;
	}
}HD_Ana_Output;

typedef map<int, HD_Ana_Output> MAP_HD_Ana_Output; // <分析判据类型nAnalyzeType，判据分析结果>

/* @brief   计算结果-有功负荷、无功负荷、功率因数、功率因数角 */
typedef struct _HD_Calc_Output
{
	string strCalcName;      // 计算结果名称
	int nCalcType;           // 计算结果类型
	float fCalcValue;        // 计算结果数据
	int nTransSide;          // 变压器侧别，线路则为1，区分类型
	int nType;               // 类型-预留

	_HD_Calc_Output()
	{
		strCalcName = "";
		nCalcType = 0;  // 枚举eCalcType列举了各计算结果类型
		fCalcValue = 999.9f;
		nTransSide = 0;
		nType = -1; // 预留
	}
}HD_Calc_Output;

typedef map<int, HD_Calc_Output> MAP_HD_Calc_Output; // <计算结果类型nCalcType，计算结果数据>

/* @brief   模拟量采样值 */
typedef struct _HD_Pt_Ai
{
	string strAiID;           // 模拟量ID
	string strAiName;         // 模拟量名称
	string strAiPrecision;    // 模拟量精度
	string strAiUnit;         // 模拟量单位
	int nAiFunType;           // 模拟量类型
	float fAiValue;           // 模拟量数据
	string strCurTime;        // 采样时间
	string strHisTime;        // 历史时间
	int nTransSide;           // 变压器侧别，线路则为1，预留防止类型无法区分
	int nType;                // 类型-支路、绕组

	_HD_Pt_Ai()
	{
		strAiID = "";
		strAiName = "";
		strAiPrecision = "";
		strAiUnit = "";
		nAiFunType = 0;
		fAiValue = 999.9f;
		strCurTime = "";
		strHisTime = "";
		nTransSide = 0;
		nType = -1; // 预留
	}
}HD_Pt_Ai;

// 内部vector用于防止同一个模拟量取到多个值
typedef map<int, vector<HD_Pt_Ai> > MAP_HD_Pt_Ai; // <模拟量类型nAiFunType，模拟量数据>

/* @brief   开关量采样值-预留 */
typedef struct _HD_Pt_Di
{
	string strDiID;           // 开关量ID
	string strDiName;         // 开关量名称
	int nDiFunType;           // 开关量功能
	int nDiValue;             // 开关量数据
	string strCurTime;        // 采样时间
	string strHisTime;        // 历史时间
	int nTransSide;           // 变压器侧别，线路则为1，预留防止类型无法区分
	int nType;                // 类型-支路、绕组

	_HD_Pt_Di()
	{
		strDiID = "";
		strDiName = "";
		nDiFunType = 0;
		nDiValue = 999;
		strCurTime = "";
		strHisTime = "";
		nTransSide = 0;
		nType = -1; // 预留
	}
}HD_Pt_Di;

// 内部vector用于防止同一个开关量取到多个值
typedef map<int, vector<HD_Pt_Di> > MAP_HD_Pt_Di; // <开关量类型nDiFunType，开关量数据>

/* @brief   定值项数据 */
typedef struct _HD_Sg_Data
{
	string strSgID;           // 定值ID
	string strSgName;         // 定值名称
	string strSgPrecision;    // 定值精度
	string strSgUnit;         // 定值单位
	int nSgFunType;           // 定值类型
	string strSgValue;        // 定值数据
	string strCurTime;        // 当前值时间
	string strHisTime;        // 历史值时间
	int nTransSide;           // 变压器侧别，线路则为1，区分类型
	int nType;                // 类型-预留

	_HD_Sg_Data()
	{
		strSgID = "";
		strSgName = "";
		strSgPrecision = "";
		strSgUnit = "";
		nSgFunType = 0; // 枚举eSgFunType列举了各定值项类型
		strSgValue = "";
		strCurTime = "";
		strHisTime = "";
		nTransSide = 0;
		nType = -1; // 预留
	}
}HD_Sg_Data;

typedef map<int, HD_Sg_Data> MAP_HD_Sg_Data; // <定值项类型nSgFunType，定值项数据>

/* @brief   测控数据-预留 */
typedef struct _HD_Ck_Data
{
	string strCkID;           // 测控ID
	string strCkName;         // 测控名称
	string strCkPrecision;    // 测控精度
	string strCkUnit;         // 测控单位
	int nCkFunType;           // 测控类型
	float fCkValue;           // 测控数据
	string strCurTime;        // 当前值时间
	string strHisTime;        // 历史值时间
	int nTransSide;           // 变压器侧别，线路则为1，预留防止类型无法区分
	int nType;                // 类型-预留

	_HD_Ck_Data()
	{
		strCkID = "";
		strCkName = "";
		strCkPrecision = "";
		strCkUnit = "";
		nCkFunType = 0; // 枚举eCkFunType列举了各测控类型
		fCkValue = 999.9f;
		strCurTime = "";
		strHisTime = "";
		nTransSide = 0;
		nType = -1; // 预留
	}
}HD_Ck_Data;

typedef map<int, HD_Ck_Data> MAP_HD_Ck_Data; // <测控类型nCkFunType，测控数据>


/** @brief   六角图参数输入模型 */
// 必填说明：保护装置ID、一次设备类型、电压等级 以及 各项具体数据
typedef struct _HEXDIAG_ANALYZE_INPUT
{
	string strPtId;                 // 保护装置ID
	string strPtName;               // 保护装置名称
	int nPtType;                    // 保护装置类型
	string strStationId;            // 所属厂站ID
	string strPrimDevId;            // 一次设备ID
	int nPrimDevType;               // 一次设备类型
	int nVoltage;                   // 电压等级
	MAP_HD_Pt_Ai mapHDPtAi;         // 模拟量数据集
	MAP_HD_Pt_Di mapHDPtDi;         // 开关量数据集
	MAP_HD_Sg_Data mapHDSgData;     // 定值项数据集
	MAP_HD_Ck_Data mapHDCkData;     // 测控数据集
	// 内部程序再用vector重组以区分支路、绕组、变压器侧别等

	_HEXDIAG_ANALYZE_INPUT()
	{
		strPtId = "";
		strPtName = "";
		nPtType = -1; // 枚举值参照"tb_secdev_base"的"type"字段
		strStationId = "";
		strPrimDevId = "";
		nPrimDevType = -1; // 枚举值参照"tb_dev_index_base"的"dev_type"字段
		nVoltage = -1;
	}
}HEXDIAG_ANALYZE_INPUT;


/** @brief   六角图分析结果输出 */
typedef struct  _HEXDIAG_ANALYZE_RESULT
{
	string strPtId;                       // 保护装置ID
	string strCurTime;                    // 当前值时间
	MAP_HD_Ana_Output mapHDAnaOutput;     // 分析结果集，按照判据类型为索引
	MAP_HD_Calc_Output mapHDCalcOutput;   // 计算结果集，vector用于区分变压器侧别
	// 输入数据入库部分
	MAP_HD_Pt_Ai mapHDPtAi;         // 模拟量数据集
	MAP_HD_Pt_Di mapHDPtDi;         // 开关量数据集
	MAP_HD_Sg_Data mapHDSgData;     // 定值项数据集
	MAP_HD_Ck_Data mapHDCkData;     // 测控数据集

	_HEXDIAG_ANALYZE_RESULT()
	{
		strPtId = "";
		strCurTime = "";
	}
}HEXDIAG_ANALYZE_RESULT;


// 适配变压器侧别-新增重构后的输入模型 zk 2024-09-24
typedef map<string, vector<HD_Pt_Ai> > MAP_MIX_Pt_Ai;      // <模拟量名称strAiName，模拟量数据>
typedef map<string, vector<HD_Pt_Di> > MAP_MIX_Pt_Di;      // <开关量名称strDiName，开关量数据>
typedef map<string, vector<HD_Sg_Data> > MAP_MIX_Sg_Data;  // <定值项名称strSgName，定值项数据>
typedef map<string, vector<HD_Ck_Data> > MAP_MIX_Ck_Data;  // <测控项名称strCkName，测控项数据>

/** @brief   MIX输入模型 */
typedef struct _HEXDIAG_ANALYZE_MIX_INPUT
{
	string strPtId;                 // 保护装置ID
	string strPtName;               // 保护装置名称
	int nPtType;                    // 保护装置类型
	string strStationId;            // 所属厂站ID
	string strPrimDevId;            // 一次设备ID
	int nPrimDevType;               // 一次设备类型
	int nVoltage;                   // 电压等级
	MAP_MIX_Pt_Ai mapMIXPtAi;        // 模拟量数据集
	MAP_MIX_Pt_Di mapMIXPtDi;        // 开关量数据集
	MAP_MIX_Sg_Data mapMIXSgData;    // 定值项数据集
	MAP_MIX_Ck_Data mapMIXCkData;    // 测控数据集

	_HEXDIAG_ANALYZE_MIX_INPUT()
	{
		strPtId = "";
		strPtName = "";
		nPtType = -1; // 枚举值参照"tb_secdev_base"的"type"字段
		strStationId = "";
		strPrimDevId = "";
		nPrimDevType = -1; // 枚举值参照"tb_dev_index_base"的"dev_type"字段
		nVoltage = -1;
	}
}HEXDIAG_ANALYZE_MIX_INPUT;

typedef struct _HEXDIAG_ANALYZE_OUTPUT
{
	int nTransSide;                       // 变压器侧别
	MAP_HD_Ana_Output mapHDAnaOutput;     // 分析结果集，按照判据类型为索引
	MAP_HD_Calc_Output mapHDCalcOutput;   // 计算结果集，按照计算类型为索引
	// 输入数据入库部分
	MAP_MIX_Pt_Ai mapMIXPtAi;             // 模拟量数据集
	MAP_MIX_Pt_Di mapMIXPtDi;             // 开关量数据集
	MAP_MIX_Sg_Data mapMIXSgData;         // 定值项数据集
	MAP_MIX_Ck_Data mapMIXCkData;         // 测控数据集

	_HEXDIAG_ANALYZE_OUTPUT()
	{
		nTransSide = 0;
	}
}HEXDIAG_ANALYZE_OUTPUT;

typedef map<int, HEXDIAG_ANALYZE_OUTPUT> MAP_MIX_Ana_Res;  // <变压器侧别nTransSide，每侧输出数据>

/** @brief   MIX输出模型 */
typedef struct  _HEXDIAG_ANALYZE_MIX_RESULT
{
	string strPtId;                       // 保护装置ID
	string strCurTime;                    // 当前值时间
	MAP_MIX_Ana_Res mapMIXAnaRes;         // 各侧的结果集

	_HEXDIAG_ANALYZE_MIX_RESULT()
	{
		strPtId = "";
		strCurTime = "";
	}
}HEXDIAG_ANALYZE_MIX_RESULT;


// 六角图分析接口
typedef int (*HEXDIAG_ANALYZE)(HEXDIAG_ANALYZE_INPUT& HexDiagParam, HEXDIAG_ANALYZE_RESULT& HexDiagResult, CMessageLog* pLog);
// 六角图分析接口-变压器/母差
typedef int (*HEXDIAG_ANALYZE_MIX)(HEXDIAG_ANALYZE_MIX_INPUT& HexDiagParam, HEXDIAG_ANALYZE_MIX_RESULT& HexDiagResult, CMessageLog* pLog);
// 接口对象释放
typedef int (*HEXDIAG_FREE)();


// 动态库名称
#ifdef OS_WINDOWS
#define LIBNAME_HEX_DIAG_ANA_HDL  "ZasHexDiagAnalyze.dll"
#endif
#ifdef OS_LINUX
#define LIBNAME_HEX_DIAG_ANA_HDL  "libZasHexDiagAnalyze.so"
#endif

class CLibMngr_HexDiagAnalyze
{
public:
	CLibMngr_HexDiagAnalyze();
	virtual ~CLibMngr_HexDiagAnalyze();

public:
	// 加载动态库和函数指针
	int Load_Lib(CMessageLog* pLog);
	// 释放动态库和函数指针
	int UnLoad_Lib();
	// 六角图二次回路带负荷检验的分析实现
	int Analyze_HexDiag_I(HEXDIAG_ANALYZE_INPUT& HexDiagParam, HEXDIAG_ANALYZE_RESULT& HexDiagResult);
	// 六角图二次回路带负荷检验的分析实现-变压器/母差
	int Analyze_HexDiag_MIX(HEXDIAG_ANALYZE_MIX_INPUT& HexDiagParam, HEXDIAG_ANALYZE_MIX_RESULT& HexDiagResult);
	// 六角图动态库的对象释放实现
	int Free_HexDiag_I();

private:
	HEXDIAG_ANALYZE m_pfun_HexDiag_Ana;
	HEXDIAG_ANALYZE_MIX m_pfun_HexDiag_Ana_MIX;
	HEXDIAG_FREE m_pfun_free;
	XJHANDLE m_dll_handle;
	CMessageLog* m_pLog;
};

#endif /*_LIBMNGR_HEXAGONAL_DIAGRAM_ANALYZE_H_*/
