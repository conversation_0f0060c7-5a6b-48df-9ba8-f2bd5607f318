/********************************************************************
Dragon 2015-04-10 Beijing.	
*********************************************************************/
#pragma  warning	(disable : 4275) 
#ifndef XJMsg104SerialNum_h__
#define XJMsg104SerialNum_h__

#include "ZxLockableObject.h"

class CXJMsg104SerialNum {
  public:
	CXJMsg104SerialNum();
	virtual ~CXJMsg104SerialNum();
	int GetSendSerial() ;
	int GetSendACKNum() ;
	int GetReceiveSerial();
	int GetReceiveACKNum();
	void SetSendSerial(int val);	
	void SetSendACKNum(int val);
	void SetReceiveSerial(int val);
	void SetReceiveACKNum(int val);
	void UpdateSendSerial();
	void UpdateReceiveSerial();
	void AssureReceive();
	void ResumeSerial();
	bool IsNeedForAssureReceive(int iSecend);
  private:

  private:
	int m_iSendSerial;
	int m_iSendACKNum;
	int m_iReceiveSerial;
	int m_iReceiveACKNum;
	time_t m_tLastTimeOfReceiveInf;
	CXJLock m_Lock;
};

#endif // XJMsg104SerialNum_h__

