#include "push.h"
        
        
void* lock_create()
{
	pthread_mutex_t* lock_key = (pthread_mutex_t*)::malloc(sizeof(pthread_mutex_t));
	int ret = pthread_mutex_init(lock_key,NULL);
	return (void*)lock_key;
	
}
void lock_destroy(void* key)
{
    if(!key) return ;
    pthread_mutex_destroy((pthread_mutex_t*)key);
    ::free(key);
}

CLIENT::CLIENT()
{
    
}
CLIENT::~CLIENT()
{
    
}

push::push(CMessageLog* pFlowLog,bool *	pExit)
{
    m_pExit = pExit;
    m_pLogFile = pFlowLog;
	listen_fd = -1;
	lck_accept = lock_create();
    lck_send = lock_create();//send
    lck_msg = lock_create();//rcv
    
    lck_cur = lock_create();//rcv
    
    lck_CloseUp = lock_create();
    lck_CloseDown = lock_create();
    lck_linkSta = lock_create();
    setUpCloseStn.clear();
    setDownCloseStn.clear();
    ThreadId = 0;
    
    m_nLinkSta = -1; // 初始化连接夶为未知
}
push::~push()
{
//    if(ThreadId != 0){
//        int nRet=pthread_join(ThreadId,NULL);
//        if(nRet != 0){
//            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop]退出 _push_thr 线程ThreadId[%d]异常，原因为:%s",m_strStnId.c_str(),ThreadId,strerror(errno));
//        }else{
//            ThreadId = 0;
//        }
//    }
	lock_destroy(lck_accept);
    lock_destroy(lck_send);
    lock_destroy(lck_msg);
    lock_destroy(lck_cur);
    lock_destroy(lck_linkSta);
    if(m_pLogFile!=NULL){
        delete m_pLogFile;
    }
}

int push::create_listensock()
{
	int fd =-1;
	fd = socket(AF_INET,SOCK_STREAM,0);
	if( fd < 0)
		return -1;

	if( fcntl(fd, F_SETFL, fcntl(fd,F_GETFL)| O_NONBLOCK) < 0)
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::create_listensock]create_listensock: Can not set Non Block.",m_strStnId.c_str());
	unsigned long arg=1;
	if( ioctl(fd, FIONBIO, &arg) < 0){
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::create_listensock]create_listensock: Can not set Non Block.",m_strStnId.c_str());
	}
    unsigned optVal;
    socklen_t optLen = sizeof(unsigned int);
    getsockopt(fd, SOL_SOCKET, SO_SNDBUF, (char*)&optVal, &optLen);
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::create_listensock]fd Buffer length: %d",m_strStnId.c_str(), optVal);
    
	/////
	sockaddr_in addr;memset(&addr,0,sizeof(addr));addr.sin_family = AF_INET;
	addr.sin_port = htons(listen_port);
	if (!bindip.empty())
		addr.sin_addr.s_addr = inet_addr(bindip.c_str());
	if( bind( fd, (sockaddr*)&addr,sizeof(addr)) < 0){
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::create_listensock]create_listensock bind err<%s>",m_strStnId.c_str(),strerror(errno) );
		closesocket(fd);
		return -1;
	}
	if( listen(fd, SOMAXCONN ) < 0){
		m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::create_listensock]create_listensock listen err<%s>",m_strStnId.c_str(),strerror(errno) );
		closesocket(fd);
		return -1;
	}
	return fd;
}

#define DELCLIENT(it)   { list<CLIENT*>::iterator tmp; tmp = it; ++it; del( c ); \
                          busy.erase(tmp);   continue; }

int push::select_loop()
{
	list<CLIENT*> busy;
    
	int ufds_cnt = 1024 ;//最大epoll数
	pollfd* ufds = (pollfd*)malloc( sizeof(pollfd)*ufds_cnt );

	int accept_err = 0;
	lock( lck_accept );
	if( listen_fd < 0) 
		while( (listen_fd =create_listensock() )< 0) {
            if(*m_pExit){
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]create_listensock 退出主循环  m_pExit[%d]",m_strStnId.c_str(),(*m_pExit));
                return 0;
            }
            sleep(30);
        }
	unlock(lck_accept);

	while( !(*m_pExit) ){
        //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()] lock",m_strStnId.c_str());
        
		int cur_cnt = busy.size() + 1 ;
		if( cur_cnt > ufds_cnt) {
			ufds_cnt = busy.size() + 2048 ;
			ufds = (pollfd*)realloc( ufds, ufds_cnt* sizeof(pollfd) );
            if (NULL == ufds)
            {
                m_pLogFile->FormatAdd(CLogFile::trace, "[%s][push::select_loop()] 申请内存失败", m_strStnId.c_str());
            }
		}
		if( listen_fd < 0 ){
			sleep(30); 
            continue;
		}
		ufds[0].fd = listen_fd;
		ufds[0].events = POLLIN; //102口的固定in
		ufds[0].revents = 0;
        //准备非102的监视的文件符和方向
		int i; list<CLIENT*>::iterator it;
		for( i=1, it = busy.begin() ; i<cur_cnt; ++i, ++it ){
			pollfd* u = &ufds[i]; //1~xx
            CLIENT* c = (*it);
			//if( c->type == TP_LOGIN ||c->type == TP_DT ){
				u->events |= POLLIN;
            //}
			u->events |= POLLOUT;
			u->revents = 0; 
			u->fd = c->sock;
		}

/* Poll the file descriptors described by the NFDS structures [starting] at
   FDS.  If TIMEOUT is nonzero and not -1, allow TIMEOUT milliseconds for
   an event to occur; if TIMEOUT is -1, block until an event occurs.
   Returns the number of file descriptors with events, zero if timed out,
   or -1 for errors.

   This function is a cancellation point and therefore not marked with
   __THROW.  */
		int status = poll( ufds, cur_cnt, 1000 );//start,maxnum,1000ms
		time_t cur = time(0);
		if( status < 0 ){
			sleep(30);
			m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]poll occur error <%s> .",m_strStnId.c_str(), strerror(errno) );
			continue;
		}
		else if( status == 0 ){//zero if timed out
            //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]------poll get timeout---status[%d][%02x]cur_cnt[%d]",m_strStnId.c_str(),status,ufds[0].revents,cur_cnt);
			//this->idle( &busy, &idle );
			continue;
		}
		//m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]------poll get ---status[%d][%02x]",m_strStnId.c_str(),status,ufds[0].revents);
        
        //先检查102口的fd-
		if(ufds[0].revents & POLLERR ){// accept error
			m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]accept error . ",m_strStnId.c_str());
			++accept_err ;
			if( accept_err > 500 ){
				accept_err =0;
				lock( lck_accept);
				closesocket( listen_fd); listen_fd = -1;
				while( (listen_fd =create_listensock() )< 0)sleep(30 );
				unlock(lck_accept);
			}
		}else 
        if(ufds[0].revents & POLLIN ){
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]fd0 POLLIN ",m_strStnId.c_str());
			int s = -1;
			lock( lck_accept);		
            //用于接受连接方的地址信息
            struct sockaddr_in clientaddr; 
            socklen_t clientlen = sizeof(clientaddr); //指定地址长度
			s = accept(ufds[0].fd, (struct sockaddr*)&clientaddr, &clientlen);
			unlock(lck_accept);
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]  accept  add[%s]port[%d]",m_strStnId.c_str(), inet_ntoa(clientaddr.sin_addr),ntohs(clientaddr.sin_port));
            if (s >= 0) {
                if (add(s, &busy, clientaddr, m_strStnId) < 0) {
                    closesocket(s);
                }
            }
            accept_err = 0;
		}
		else{
			accept_err =0;
		}
        
        //检查所有监视
        //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]do all busy[%d]",m_strStnId.c_str(),cur_cnt-1);
		for( i=1, it = busy.begin() ; i<cur_cnt; ++i ){//不含Fd0[102端口的]
            if (it == busy.end()) break;
			pollfd* u = &ufds[i];
            CLIENT* c = (*it);
            if( (u->revents & POLLIN) || ( u->revents & POLLOUT) ){
                if(c!=NULL){
                    //m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]fd i[%d]c->type[%d],c=NULL,c->tmo[%p]",m_strStnId.c_str(),i,c->type,&c->tmo);
                    lock(lck_cur);
                    c->tmo = cur;
                    unlock(lck_cur);
                }else{
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]fd i[%d] c=NULL",m_strStnId.c_str(),i);
                    continue;
                }
            }
            //m_pLogFile->FormatAdd(CLogFile::trace,"i[%d]event[%02x]",i ,u->revents);
			if( u->revents & POLLERR ){
                m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]fd i[%d]c->type[%d],POLLERR",m_strStnId.c_str(),i,c->type);
				DELCLIENT(it);
			}
			if( u->revents & POLLIN){
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]fd i[%d]c->type[%d],POLLIN 读缓冲区可读",m_strStnId.c_str(),i,c->type);
				//c->tmo = cur;
				if( rsock( c, &busy ) < 0){
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]fd i[%d]c->type[%d],POLLIN rsock err del",m_strStnId.c_str(),i,c->type);
					DELCLIENT(it);
                }
			}
			if( u->revents & POLLOUT){//send or  sendok
                //m_pLogFile->FormatAdd(CLogFile::trace,"fd[%d]c->type[%x],POLLOUT 写缓冲区可写",i,c->type);
				//c->tmo = cur;
				if( wsock( c ) < 0){
                    m_pLogFile->FormatAdd(CLogFile::error,"[%s][push::select_loop()]fd i[%d]c->type[%x],POLLOUT wsock err del",m_strStnId.c_str(),i,c->type);
					DELCLIENT(it);
                }
			}
			++it;
		}
        
        //lock(lck_CloseDown);
        //if(!setDownCloseStn.empty()){//子站断了 
        //    setDownCloseStn.clear();
        //    cur_cnt = busy.size() + 1 ;//因为busy可能变少了避免空指针，重拿下现在的
        //    for( i=1, it = busy.begin() ; i<cur_cnt; ++i ){
        //        CLIENT* c = (*it);
        //        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop]子站断了busy del and close fd[%d]",m_strStnId.c_str(),(*it)->sock);
        //        DELCLIENT(it);
        //        ++it;
        //    }
        //}
        //unlock(lck_CloseDown);]
        lock(lck_send);
        if (listSendMsg.size() > 20000 && 0 == busy.size()) {
            listSendMsg.pop_front();
        }
        unlock(lck_send);

        MySleep(1);
        //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]endpoll-----next poll cur_cnt[%d]",m_strStnId.c_str(),cur_cnt);
	}
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::select_loop()]退出主循环  m_pExit[%d]",m_strStnId.c_str(),(*m_pExit));
    
	return 0;
}

int push::add(int fd, list<CLIENT*>* busy,sockaddr_in addr,string strStnId)
{
    int nRet=0;
    //int n=busy->size();
    //if(n>0){
    //    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::add]client重复,拒绝掉这个c,关闭同名的老的站.busy[%d]",m_strStnId.c_str(),n);
    //    //删除老的,为新的腾路
    //    lock(lck_CloseDown);
    //    for(list<CLIENT*>::iterator itor=busy->begin(); itor!=busy->end(); itor++ ){
    //        close((*itor)->sock);
    //        delete(*itor);
    //    }
    //    busy->clear();
    //    unlock(lck_CloseUp);
    //}
       
    CLIENT* c = 0;
    c = new CLIENT; if (!c) return -1;
    fcntl(fd, F_SETFL, fcntl(fd, F_GETFL) | O_NONBLOCK);
    unsigned long arg = 1;
    ioctl(fd, FIONBIO, &arg);
    c->sock = fd;
    c->type = TP_LOGIN;
    c->tmo = time(0);
    //send
    c->curr_trans = 0;
    c->total_trans = BUF_SZ - 1;
    //rcv
    c->curr_Rcv = 0;
    c->total_Rcv = BUF_SZ - 1;
    //manager
    c->addr = addr;
    c->strStnId = strStnId.c_str();
    c->bFirstDtInit = true;
    c->bRecvCR = false;
    busy->push_back(c);
    m_pLogFile->FormatAdd(CLogFile::trace, "[%s][push::add]fd[%d]busy[%d]", m_strStnId.c_str(), fd, busy->size());

    setLinkSta(1);; // 新的连接 

	return nRet;
}
int push::del(CLIENT* c)
{
    setLinkSta(0);
    
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::del]close socket fd[%d]",m_strStnId.c_str(),c->sock);
	if( (c->sock>=0)&&(c->sock<=65535) ){
		closesocket(c->sock);
		c->sock = -1;
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::del]close socket fd[%d] delete",m_strStnId.c_str(),c->sock);
        delete(c);
	}else{
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::del]close socket fd[%d]<0  not delete",m_strStnId.c_str(),c->sock); 
    }
    
    lock(lck_CloseUp);
    setUpCloseStn.insert(m_strStnId.c_str());     
    unlock(lck_CloseUp);
    
    lock(lck_msg);
    vMsg.clear();
    unlock(lck_msg);
    
    //lock(lck_send);
    //listSendMsg.clear();
    //unlock(lck_send);
    
    
    return 0;
}
int push::rsock(CLIENT* c, list<CLIENT*>* busy)
{
//        https://www.rfc-editor.org/rfc/rfc1006.txt
//byte    0               1             2             3                 4
//x10     0                   1                   2                   3
//bit     0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
//       +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//       |      vrsn     |    reserved   |          packet length        |
//       +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
//       packet length                16 bits (min=7, max=65535)
    
	switch(c->type)
	{
        default:
		{
            int r = 0;
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::rsock] TP_LOGIN recv  head",m_strStnId.c_str());
            while (c->curr_Rcv < RFC1006_HEAD_LEN) {//4byte: to get all length
                r = recv (c->sock, c->rcvBuf + c->curr_Rcv, RFC1006_HEAD_LEN - c->curr_Rcv, 0);
                _CHECK_BLOCK	//0 if peer disconnected, negative if error.
                c->curr_Rcv += r;	
            }
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::rsock] TP_LOGIN recv head OK ",m_strStnId.c_str());
//            int i=0;
//            for(i=0;i<c->curr_Rcv;i++){
//                m_pLogFile->FormatAdd(CLogFile::trace,",0x%02x",(unsigned char)c->rcvBuf[i]);
//            }
//            m_pLogFile->FormatAdd(CLogFile::trace,"");
            
            if (c->rcvBuf[0] != RFC1006_VERSION){
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::rsock]TP_END_CONNECTION TP0 TPDU too short to decode. TP_LOGIN Ignored.RFC1006_VERSION" ,m_strStnId.c_str());
                //c->type = TP_END_CONNECTION;
                return 0;
            }
            
            unsigned short tpkt_len=0;
            tpkt_len = ( ( (unsigned short) c->rcvBuf[2] ) << 8 ) | (unsigned char)c->rcvBuf[3];
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::rsock] TP_LOGIN 0x[%04x]=[%hu]",m_strStnId.c_str(),tpkt_len,tpkt_len);
            if ( tpkt_len == 0) {//unsigned short范围:0 ~ 65535 || tpkt_len > 65535 
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::rsock]TP_END_CONNECTION TP0 TPDU too short to decode.TP_LOGIN  Ignored.65535",m_strStnId.c_str() );
                //c->type = TP_END_CONNECTION;
                return 0;
            }
            
            while (c->curr_Rcv<tpkt_len){
                r = recv (c->sock, c->rcvBuf + c->curr_Rcv, tpkt_len - c->curr_Rcv, 0);
                _CHECK_BLOCK	//0 if peer disconnected, negative if error.
                c->curr_Rcv += r;	
            }
			c->curr_Rcv = 0; 
			c->total_Rcv = tpkt_len ;
            //c->total_Rcv = tpkt_len ;
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::rsock] read all TP_LOGIN [%hu]",m_strStnId.c_str(),tpkt_len);
            
//            int i=0;
//            for(i=0;i<tpkt_len;i++){
//                m_pLogFile->FormatAdd(CLogFile::trace,",0x%02x",(unsigned char)c->rcvBuf[i]);
//            }
//            m_pLogFile->FormatAdd(CLogFile::trace,"");
            
            filterTpkt( c, busy );
		}
		break;
	}
	return 0;
}
int push::wsock(CLIENT* c)
{
	int r=1;
    string str1Send;
    
    
    //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::wsock]r[%d]listSendMsg.empty[%d]lock",m_strStnId.c_str(),r,listSendMsg.empty());
    lock(lck_send);
    if(!listSendMsg.empty()){
        str1Send = listSendMsg.front();
        listSendMsg.pop_front();
    }
    unlock(lck_send);
    
    if(!str1Send.empty()){
        memcpy(c->buf, str1Send.data(), str1Send.length());
        c->curr_trans = 0;
        c->total_trans = str1Send.length();
        r = wnonblock(c);
    }
    //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::wsock]r[%d]listSendMsg.empty[%d]c->type[%d]",m_strStnId.c_str(),r,listSendMsg.empty(),c->type);
    
	if(r<=0) {
        return r;//POLLOUT 写成功
    }
    
	switch(c->type) {
		case TP_END_CONNECTION:
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::wsock]TP_END_CONNECTION TP0 TPDU TP_END_CONNECTION.",m_strStnId.c_str() );
			return -3;
			break;
        default:
            break;
	}
    //m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::wsock]r[%d]listSendMsg.empty[%d]c->type[%d] return 0 ",m_strStnId.c_str(),r,listSendMsg.empty(),c->type);
	return 0;
}

int push::filterTpkt(CLIENT* c, list<CLIENT*>* busy)
{
    char * pdu_ptr = c->rcvBuf;
    int pdu_len = c->total_Rcv;
    
//    m_pLogFile->FormatAdd(CLogFile::trace,"filterTpkt()");
//    int i=0;
//    for(i=0;i<pdu_len;i++){
//        m_pLogFile->FormatAdd(CLogFile::trace,"[%02x]",(ST_UCHAR)pdu_ptr[i]);
//    }
//    m_pLogFile->FormatAdd(CLogFile::trace,"");
    
    
    if ( pdu_len < RFC1006_HEAD_LEN + 2 ) {//TPDU must be at least 2 bytes (len and TPDU code) to safely decode
        m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] TP_END_CONNECTION TP0 TPDU too short to decode. Ignored." ,m_strStnId.c_str());
        //c->type = TP_END_CONNECTION;
        return -1;
    }
    pdu_len -= RFC1006_HEAD_LEN;//Strip off TPKT header. 
    pdu_ptr += RFC1006_HEAD_LEN;

    /* get the TPDU type (from the second octet, bits 4-7) */
    unsigned char type = pdu_ptr [1] & TP_PDU_MASK_CDT;
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt RFC1006_HEAD_LEN[%d]pdu_len[%d]type[%d]",m_strStnId.c_str(),RFC1006_HEAD_LEN,pdu_len,type);
    /* Based on type of TPDU (CR, CC, DT, etc.) call appropriate decode */
    /* subfunction.                         */
    
    switch ( type )
    {
        case TP_PDU_TYPE_CR:
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt TP_PDU_TYPE_CR",m_strStnId.c_str());
            
            memcpy(c->buf,c->rcvBuf,c->total_Rcv);
            c->buf[5]=0xd0;//cc回复
            c->curr_trans=0; 
            c->total_trans = c->total_Rcv;
            c->bRecvCR = true;

            for (list<CLIENT*>::iterator itor = busy->begin(); itor != busy->end();) {
                if ((*itor)->bRecvCR && (*itor) != c)
                {
                    close((*itor)->sock);
                    delete(*itor);
                    itor = busy->erase(itor);
                }
                else
                {
                    ++itor;
                }
            }

            wnonblock(c);
            
            //c->type = TP_RECR;
            break;
        case TP_PDU_TYPE_CC:
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt TP_PDU_TYPE_CC",m_strStnId.c_str());
            break;

        case TP_PDU_TYPE_DR:
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt TP_PDU_TYPE_DR",m_strStnId.c_str());
            break;

        case TP_PDU_TYPE_DT:
            {
                m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt TP_PDU_TYPE_DT",m_strStnId.c_str());
                string strTpkt;
                strTpkt.assign(c->rcvBuf,c->total_Rcv);
                bzero(c->rcvBuf,sizeof(c->rcvBuf));
                //string str1;
                //str1.assign(InitReqMsg,sizeof(InitReqMsg));
                //PrintBytes(str1);
                //PrintBytes(strTpkt);
                //if(2 == isSubarray(strTpkt,str1)){
                if(c->bFirstDtInit){
                    c->bFirstDtInit =false;
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] ======init=====",m_strStnId.c_str());
                    
                    memcpy(c->buf,InitResponMsg,sizeof(InitResponMsg));
                    c->curr_trans  = 0; 
                    c->total_trans = sizeof(InitResponMsg);
                    wnonblock(c);
                    
                    
                }else{
                    lock(lck_msg);
                    vMsg.push_back(strTpkt);
                    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt  c->vMsg.size[%d]",m_strStnId.c_str(),vMsg.size());
                    unlock(lck_msg);
                }
                //c->type = TP_REDT; 
            }
            break;

        case TP_PDU_TYPE_ER:
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt TP_PDU_TYPE_ER",m_strStnId.c_str());
            break;

        case TP_PDU_TYPE_ED:    /* we do not support expedited data */
            m_pLogFile->FormatAdd(CLogFile::trace, "[%s][push::filterTpkt] tpkt TP_PDU_TYPE_ED",m_strStnId.c_str() );
            break;
        case TP_PDU_TYPE_EA:
            m_pLogFile->FormatAdd(CLogFile::trace, "[%s][push::filterTpkt] tpkt TP-ERROR: decode ED or EA TPDU failed (not supported)" ,m_strStnId.c_str());
            break;      /* Do nothing                   */

        default:        /* type unknown, we should exit decoding    */
            m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::filterTpkt] tpkt TP-ERROR: decode TPDU failed (invalid type 0x%2.2X)",m_strStnId.c_str(), ( unsigned ) type );
            break;
    }
}
//有需要立即返回的
int push::wnonblock(CLIENT* c)
{
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::wnonblock] wnonblock()[%d]",m_strStnId.c_str(),c->total_trans - c->curr_trans);
    
	if( c->curr_trans >= c->total_trans){
		return 1;
    }
    
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s][push::wnonblock] MMS wnonblock() can do [%d]",m_strStnId.c_str(), c->total_trans-c->curr_trans);
    //PrintBytes(c->buf,c->total_trans - c->curr_trans);
	////
	int bb = c->total_trans - c->curr_trans;
    const int MAX_SEND = 2048;
	int cur = 0; 
    if(bb > MAX_SEND) return 2;
    int total = bb > MAX_SEND ? MAX_SEND : bb;
	while( cur < total ){
		int r = send(c->sock, c->buf+c->curr_trans, total - cur , 0);
		_CHECK_BLOCK;
		c->curr_trans += r;
		cur  += r;
	}
    
	if( c->curr_trans < c->total_trans ){
		return -1;
    }
    
    return 0;
}
void* push::_push_thr(void*_p)
{
	push* p = (push*)_p;
	p->select_loop();
   
	return 0;
}
//
//void* push::_push_Rcv_thr(void*_p)
//{
//	push* p = (push*)_p;
//	p->select_Rcvloop();
//   
//	return 0;
//}
//void* push::_push_Send_thr(void*_p)
//{
//	push* p = (push*)_p;
//	p->select_Sendloop();
//   
//	return 0;
//}

int push::start(string bindip,int port,string strStnId,map<string,pthread_t > &mapThreadIds)
{
    this->bindip = bindip.c_str();
	this->listen_port = port;
    this->m_strStnId = strStnId.c_str();
    
    //_push_thr
    int nRet = pthread_create(&ThreadId,NULL, _push_thr, this);
    if( nRet != 0 ){
        m_pLogFile->FormatAdd(CLogFile::error,"[push::Start()] _push_thr 线程 启动时失败 原因:%s(%d)。",strerror(errno),errno);
        return -1;
	}else{
        m_pLogFile->FormatAdd(CLogFile::trace,"[push::Start()] _push_thr 线程ID[%ld] 启动成功。",ThreadId);
	}
    //pthread_detach(ThreadId);
    mapThreadIds.insert(make_pair("_push_thr",ThreadId));
    
//    //_push_Rcv_thr
//    nRet = pthread_create(&RcvThreadId,NULL, _push_Rcv_thr, this);
//    if( nRet != 0 ){
//        m_pLogFile->FormatAdd(CLogFile::error,"[push::Start()] _push_Rcv_thr 线程 启动时失败 原因:%s(%d)。",strerror(errno),errno);
//        return -2;
//	}else{
//        m_pLogFile->FormatAdd(CLogFile::trace,"[push::Start()] _push_Rcv_thr 线程ID[%ld] 启动成功。",RcvThreadId);
//	}
//    pthread_detach(RcvThreadId);
//    mapThreadIds.insert(make_pair("_push_Rcv_thr",RcvThreadId));
//    
//    //_push_Send_thr
//    nRet = pthread_create(&SendThreadId,NULL, _push_Send_thr, this);
//    if( nRet != 0 ){
//        m_pLogFile->FormatAdd(CLogFile::error,"[push::Start()] _push_Send_thr 线程 启动时失败 原因:%s(%d)。",strerror(errno),errno);
//        return -3;
//	}else{
//        m_pLogFile->FormatAdd(CLogFile::trace,"[push::Start()] _push_Send_thr 线程ID[%ld] 启动成功。",SendThreadId);
//	}
//    pthread_detach(SendThreadId);
//    mapThreadIds.insert(make_pair("_push_Send_thr",SendThreadId));
//    
//    
    
	return 0;
}

int push::PrintBytes(const string &str)
{
    vector<BYTE> vBytes;
    vBytes.insert(vBytes.end(),str.begin(),str.end());
    string str1;str1.clear();
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s] [push::PrintBytes]",m_strStnId.c_str());
    for(vector<BYTE>::iterator it=vBytes.begin();it!=vBytes.end();it++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)*it);
        str1=str1+c20;
    }
    m_pLogFile->FormatAdd(CLogFile::trace,"%s",str1.c_str());

    
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s] [push::PrintBytes]  end ",m_strStnId.c_str());
}
int push::PrintBytes( char *message,const int message_len)
{
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s] [push::PrintBytes2]",m_strStnId.c_str());
    string str1;str1.clear();
    for(int i=0;i<message_len;i++){
        char c20[20];
        snprintf(c20,20,"%02x ",(unsigned char)message[i]);
        str1=str1+c20;
    }
    m_pLogFile->FormatAdd(CLogFile::trace,"%s",str1.c_str());
    m_pLogFile->FormatAdd(CLogFile::trace,"[%s] [push::PrintBytes2]  end ",m_strStnId.c_str());
}

 int push::getLinkSta()
 {
     int nSta = -1;
     
     lock(lck_linkSta);
     nSta = m_nLinkSta;     
     unlock(lck_linkSta);
     
     return nSta;
 }
 
 void push::setLinkSta(int nSta)
 {
     lock(lck_linkSta);
     m_nLinkSta = nSta;     
     unlock(lck_linkSta);
     m_pLogFile->FormatAdd(CLogFile::trace,"[%s] [push::setLinkSta] 前置连接状态变更 ，当前状态:%d [0:断开，1:连接 ",m_strStnId.c_str(),nSta);
 }

