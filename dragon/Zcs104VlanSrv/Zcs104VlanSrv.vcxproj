﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{64FF2631-2BF8-4B3D-8961-1FA71AF9C8AA}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>Zcs104VlanSrv</RootNamespace>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="APCI\common\ZxMsgCaster.cpp" />
    <ClCompile Include="APCI\pro\common\Zx104Len2CpuMsgAttach.cpp" />
    <ClCompile Include="APCI\pro\common\Zx104MsgAttach.cpp" />
    <ClCompile Include="APCI\pro\common\ZxASDUHandler.cpp" />
    <ClCompile Include="APCI\pro\common\ZxComFunction.cpp" />
    <ClCompile Include="APCI\pro\ZcsHn104VlanCliPro\ZxGB103MsgAttachFactory.cpp" />
    <ClCompile Include="APCI\pro\ZcsHn104VlanCliPro\ZxHuNTc103ASDUHandler.cpp" />
    <ClCompile Include="APCI\pro\ZcsHn104VlanFlow\Zx103CacheIndex.cpp" />
    <ClCompile Include="APCI\pro\ZcsHn104VlanFlow\ZxPro103ClientWay.cpp" />
    <ClCompile Include="APCI\pro\ZcsProApciHdl\Zx104APCIHandler.cpp" />
    <ClCompile Include="APCI\pro\ZcsProApciHdl\ZxAPCIHandler.cpp" />
    <ClCompile Include="APCI\pro\ZcsProApciHdl\ZxMsg104SerialNum.cpp" />
    <ClCompile Include="DevFlowManage.cpp" />
    <ClCompile Include="DeviceObj.cpp" />
    <ClCompile Include="Main.cpp" />
    <ClCompile Include="MMS\MMSAnalyze.cpp" />
    <ClCompile Include="Msg103.cpp" />
    <ClCompile Include="push.cpp" />
    <ClCompile Include="push103.cpp" />
    <ClCompile Include="SecDevFlowModule.cpp" />
    <ClCompile Include="TaskMngr.cpp" />
    <ClCompile Include="Zcs104VlanSrv_update_history.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="APCI\common\CommuDef.h" />
    <ClInclude Include="APCI\common\ZxMsgCaster.h" />
    <ClInclude Include="APCI\common\ZxProtocolAPI.h" />
    <ClInclude Include="APCI\pro\common\Zx103ProtocolDef.h" />
    <ClInclude Include="APCI\pro\common\Zx104Len2CpuMsgAttach.h" />
    <ClInclude Include="APCI\pro\common\Zx104MsgAttach.h" />
    <ClInclude Include="APCI\pro\common\Zx104MsgAttachFactory.h" />
    <ClInclude Include="APCI\pro\common\ZxAPCIInterFace.h" />
    <ClInclude Include="APCI\pro\common\ZxASDUHandler.h" />
    <ClInclude Include="APCI\pro\common\ZxASDUInterFace.h" />
    <ClInclude Include="APCI\pro\common\ZxCommonDBFunction.h" />
    <ClInclude Include="APCI\pro\ZcsHn104VlanCliPro\ZxGB103MsgAttachFactory.h" />
    <ClInclude Include="APCI\pro\ZcsHn104VlanCliPro\ZxHuNTc103ASDUHandler.h" />
    <ClInclude Include="APCI\pro\ZcsHn104VlanFlow\Zx103CacheIndex.h" />
    <ClInclude Include="APCI\pro\ZcsHn104VlanFlow\ZxPro103ClientWay.h" />
    <ClInclude Include="APCI\pro\ZcsProApciHdl\Zx104APCIHandler.h" />
    <ClInclude Include="APCI\pro\ZcsProApciHdl\ZxAPCIHandler.h" />
    <ClInclude Include="APCI\pro\ZcsProApciHdl\ZxMsg104SerialNum.h" />
    <ClInclude Include="DevFlowManage.h" />
    <ClInclude Include="DeviceObj.h" />
    <ClInclude Include="MMS\MMSAnalyze.h" />
    <ClInclude Include="MMS\MMSdef.h" />
    <ClInclude Include="Msg103.h" />
    <ClInclude Include="push.h" />
    <ClInclude Include="push103.h" />
    <ClInclude Include="SecDevFlowModule.h" />
    <ClInclude Include="TaskMngr.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Makefile" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>