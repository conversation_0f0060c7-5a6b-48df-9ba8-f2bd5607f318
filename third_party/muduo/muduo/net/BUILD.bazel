cc_library(
    name = "net",
    srcs = [
        "Acceptor.cc",
        "Buffer.cc",
        "Channel.cc",
        "Connector.cc",
        "EventLoop.cc",
        "EventLoopThread.cc",
        "EventLoopThreadPool.cc",
        "InetAddress.cc",
        "Poller.cc",
        "Socket.cc",
        "SocketsOps.cc",
        "TcpClient.cc",
        "TcpConnection.cc",
        "TcpServer.cc",
        "Timer.cc",
        "TimerQueue.cc",
        "poller/DefaultPoller.cc",
        "poller/EPollPoller.cc",
        "poller/PollPoller.cc",
    ],
    hdrs = [
        "Acceptor.h",
        "Buffer.h",
        "Callbacks.h",
        "Channel.h",
        "Connector.h",
        "Endian.h",
        "EventLoop.h",
        "EventLoopThread.h",
        "EventLoopThreadPool.h",
        "InetAddress.h",
        "Poller.h",
        "Socket.h",
        "SocketsOps.h",
        "TcpClient.h",
        "TcpConnection.h",
        "TcpServer.h",
        "Timer.h",
        "TimerId.h",
        "TimerQueue.h",
        "poller/EPollPoller.h",
        "poller/PollPoller.h",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//muduo/base",
    ],
)
