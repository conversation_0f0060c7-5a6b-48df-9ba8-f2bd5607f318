---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

DELETE FROM HERE TO LINE 18 BEFORE OPEN AN ISSUE.

GitHub issues are for tracking bugs, not for general discussing like a forum.

If you have a general question to ask, use GitHub discussions:

https://github.com/chenshuo/muduo/discussions

When filing an issue of muduo, please provide a [SSCCE](http://sscce.org):
Short, Self Contained, Correct (Compilable), Example.

If you can't compile muduo, make sure you install `cmake` and `boost` from the
official package repository, e.g. `apt` or `yum`, before opening a bug.
Don't open a bug if you installed boost from a third-party source or
downloaded it by yourself, and couldn't compile muduo, thank you.

Also specify the exact environment where the issue occurs:

## Linux distro and version? x86 or ARM? 32-bit or 64-bit?

## Branch (cpp98/cpp11/cpp17) and version of muduo?

## Version of cmake, gcc and boost? (If not from distro.)
