package logging;

message LogRecord {
  // must present in first message
  message Heartbeat {
    required string hostname = 1;
    required string process_name = 2;
    required int32 process_id = 3;
    required int64 process_start_time = 4; // microseconds sinch epoch
    required string username = 5;
  }

  optional Heartbeat heartbeat = 1;
  // muduo/base/Logging.h
  // enum LogLevel
  // {
  //   TRACE, // 0
  //   DEBUG, // 1
  //   INFO,  // 2
  //   WARN,  // 3
  //   ERROR, // 4
  //   FATAL, // 5
  // };
  required int32 level = 2;
  required int32 thread_id = 3;
  required int64 timestamp = 4; // microseconds sinch epoch
  required string message = 5;
  // optional: source file, source line, function name
}
