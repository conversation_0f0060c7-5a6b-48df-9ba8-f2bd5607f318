
#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;

events {
    worker_connections  1024;
}

http {
    #include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;
    access_log      off;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    upstream muduo_backend {
        server localhost:19981;
        #server localhost:19982;
        keepalive 32;
    }

    server {
        listen       10080;
        server_name  localhost;

        #access_log  logs/host.access.log  main;

        location / {
            root   html;
            index  index.html index.htm;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   html;
        }

        # pass /sudoku/ to muduo FastCGI server listening on 127.0.0.1:19981
        #
        location /sudoku/  {
            fastcgi_keep_conn   on;
            fastcgi_pass        muduo_backend;
            #include             fastcgi_params;
            #fastcgi_param  QUERY_STRING       $query_string;
            #fastcgi_param  REQUEST_METHOD     $request_method;
            #fastcgi_param  CONTENT_TYPE       $content_type;
            #fastcgi_param  CONTENT_LENGTH     $content_length;

            #fastcgi_param  SCRIPT_NAME        $fastcgi_script_name;
            fastcgi_param  REQUEST_URI        $request_uri;
            #fastcgi_param  DOCUMENT_URI       $document_uri;
            #fastcgi_param  DOCUMENT_ROOT      $document_root;
            #fastcgi_param  SERVER_PROTOCOL    $server_protocol;
            #fastcgi_param  HTTPS              $https if_not_empty;

            #fastcgi_param  GATEWAY_INTERFACE  CGI/1.1;
            #fastcgi_param  SERVER_SOFTWARE    nginx/$nginx_version;

            #fastcgi_param  REMOTE_ADDR        $remote_addr;
            #fastcgi_param  REMOTE_PORT        $remote_port;
            #fastcgi_param  SERVER_ADDR        $server_addr;
            #fastcgi_param  SERVER_PORT        $server_port;
            #fastcgi_param  SERVER_NAME        $server_name;
        }

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }
}
