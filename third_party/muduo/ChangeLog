2018-10-22   <PERSON><PERSON>  <<EMAIL>>
  * Last version in C++98/03, next version will use C++11
  * Enable Clang Thread Safety Analysis.
  * Fix "off_t does not name a type" for CentOS 7 (#316) by qiao hai-jun
  * Fix warnings for gcc 8.
  * Add ttcp asio examples
  * Implement Procmon::listFiles()

2018-01-18   <PERSON><PERSON>  <<EMAIL>>
  * Fix race condition between Thread::tid() and Thread::start().
  * Change protorpc format, add back package name. (Go does this since 1.2)
  * examples/socks4a/tunnel.h stops reading if output buffer is full.
  * Fixes for GCC 6/7
  * Minor fixes by huntin<PERSON>, lian<PERSON><PERSON>cong, zhouday<PERSON>2, octocat_lee,
  jack.x<PERSON><PERSON><PERSON>, l<PERSON><PERSON>, yqsy.
  * Version 1.1.0

2016-10-25   <PERSON><PERSON>  <<EMAIL>>
  * Add Travis CI
  * Add EvevtLoop::queueSize() by <zhuang<PERSON>23>
  * Implement TcpClient::retry() by fdxuwei
  * Change Condition::waitFor<PERSON><PERSON>onds() parameter type from int to double by <PERSON><PERSON>hu
  * Minor fixes by <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, wuz<PERSON><PERSON>
  * Version 1.0.9

2016-02-11   Shuo Chen  <<EMAIL>>
  * Preliminary support of IPv6.
  * Add stop/startRead in TcpConnection by <zhang.jako>
  * Version 1.0.8

2015-11-09   Shuo Chen  <<EMAIL>>
  * Add stats to Sudoku examples.
  * Add example of PeriodicTimer class.
  * Add thrift examples by <decimalbell>.
  * Move hiredis example by <decimalbell> to contrib/.
  * Move HTTP parseRequest to HttpContext class by <decimalbell>.
  * Other fixes from <harrywong>, <cfreestar>, <qlhuangrui>, <lidw1988>.
  * Version 1.0.7

2015-04-03   Shuo Chen  <<EMAIL>>
  * Fix ProcessInspector::threads().
  * Minor fixes and improvements from liyuan989 and zieckey.
  * More Sudoku examples.
  * Version 1.0.6

2015-01-30   Shuo Chen  <<EMAIL>>
  * Add examples/procmon
  * EventLoop supports set/get context by <zieckey>
  * Fix bug #107
  * Version 1.0.5

2014-10-05   Shuo Chen  <<EMAIL>>
  * Enrich interfaces of EventLoopThreadPool by <zieckey>
  * Buffer supports reading int64_t by <alisper>
  * Add hiredis example by <decimalbell>
  * Fix bug about TcpClient life time again.
  * Other minor fixes, including some from <huahang>
  * Version 1.0.4

2014-08-02   Shuo Chen  <<EMAIL>>
  * Singleton supports 'no_destroy'.
  * Get tcp_info in TcpConnection.
  * Add CurrentThread::tidStringLength().
  * Fix bug about TcpClient life time. More checks.
  * Version 1.0.3

2014-06-30   Shuo Chen  <<EMAIL>>
  * Fix boundary check in Buffer::findEOL() by <renxingsong>.
  * Fix typos in InetAddress.cc by <huangml.zh>.
  * Fix 32-bit integer overflow bug in time_client by <guochy2012>.
  * Update comments in Buffer::readFd() by <huahang>.
  * Add ThreadPool::setThreadInitCallback().
  * Rename GzipStream to ZlibStream.
  * Version 1.0.2

2014-04-10   Shuo Chen  <<EMAIL>>
  * More ProcessInfo functions.
  * Add GzipFile (in C++11 only) and GzipOutputStream.
  * Add SystemInspector.
  * muduo::Threads now sets thread name with prctl().
  * Version 1.0.1

2014-03-12   Shuo Chen  <<EMAIL>>
  * Add TCP and RPC balancer examples
  * Version 1.0.0

2014-03-05   Shuo Chen  <<EMAIL>>
  * Introduce class StringArg for passing C-style string arguments.
  * Support localtime in logging.
  * Version 1.0.0-rc2

2014-02-22   Shuo Chen  <<EMAIL>>
  * Default to release build.
  * Version 1.0.0-rc1

2014-02-22   Shuo Chen  <<EMAIL>>
  * Add base/WeakCallback.h
  * Add TcpConnection::forceCloseWithDelay().
  * Add InetAddress::resolve for sync DNS resolving.
  * Add simple Protobuf codec for single message type.
  * Add ACE ttcp and logging examples.
  * Fix race conditoin in RpcChannel::CallMethod().
  * Version 0.9.8

2014-01-11   Shuo Chen  <<EMAIL>>
  * Add TcpConnection::forceClose().
  * Add fastcgi nginx.conf example
  * Fix iterator invalidation in hub.cc.
  * Version 0.9.7

2013-10-21   Shuo Chen  <<EMAIL>>
  * Minor fixes.
  * Version 0.9.6

2013-08-31   Shuo Chen  <<EMAIL>>
  * Add C++11 rvalue overloads for boost::function parameters
  * Add PerformanceInspector, support remote profiling with gperftools
  * Add examples of memcached server and client
  * Version 0.9.5

2013-07-28   Shuo Chen  <<EMAIL>>
  * Protobuf RPC wire protocol changed,
    package name removed in 'service' field.
  * Add roundtrip_udp as a UDP example
  * More inspect
  * Fix Connector::stop()
  * Fix for protobuf 2.5.0
  * Version 0.9.4

2013-05-11   Shuo Chen  <<EMAIL>>
  * ThreadPool can be blocking
  * Support SO_REUSEPORT, added in kernel 3.9.0
  * Fix Mutex::isLockedByThisThread()
  * Version 0.9.3

2013-03-22   Shuo Chen  <<EMAIL>>
  * Fix bugs
  * Add Sudoku client
  * Version 0.9.2

2013-01-16   Shuo Chen  <<EMAIL>>
  * Fix bug introduced in dd26871
  * Version 0.9.1

2013-01-09   Shuo Chen  <<EMAIL>>
  * Add single thread concurrent download example in examples/curl.
  * Add distributed word counting example.
  * Add simple FastCGI example.
  * Fix HttpRequest for empty header value, contributed by SeasonLee
  * Fix Connector destruction
  * Version 0.9.0

2012-11-06   Shuo Chen  <<EMAIL>>
  * Version for the book
  * Fix Buffer::shrink()
  * Fix race condition of ThreadPool::stop()
  * Version 0.8.2

2012-09-30   Shuo Chen  <<EMAIL>>
  * Add Channel::remove()
  * Logger::SourceFile supports char*
  * Fix for g++ 4.7
  * Version 0.8.1

2012-09-06   Shuo Chen  <<EMAIL>>
  * More Buffer member functions, contributed by SeasonLee
  * Add unit tests for Buffer
  * Fix wait condition in AsyncLogging::threadFunc()
  * Rename fromHostPort to fromIpPort
  * Add hash_value for shared_ptr
  * Add TcpConnection::getMutableContext()
  * Remove unnecessary code, header
  * Add another example in idleconnection
  * Version 0.8.0

2012-06-26   Shuo Chen  <<EMAIL>>

  * Add TimeZone class and unit tests.
  * Inline Buffer::appendInt32() and Buffer::peekInt32().
  * Catch exception in Thread::runInThread().
    Rethrow in catch(...) to make pthread_cancel() working.
  * Avoid deleting incomplete types.
  * Replace delete with boost::ptr_vector
  * Destructs ThreadLocalSingleton
  * Replace __thread object with ThreadLocalSingleton in examples/asio/chat/
  * Fix compile with g++ 4.6
  * With armlinux.diff, muduo compiles on Raspberry Pi with g++ 4.5.
  * Version 0.7.0

2012-06-11   Shuo Chen  <<EMAIL>>

  * Put hostname as part of log file name.
  * Extract muduo/base/CurrentThread.h
  * Optimize logging for thread id and source filename.
  * Add BlockingQueue_bench, improve Thread_bench.
  * Add examples/zeromq, for round-trip latency tests.
  * Demonstrate HighWaterMark callback and weak callback in tcp tunnel.
  * Fix chat codec for invalid length.
  * Version 0.6.0

2012-06-03  Shuo Chen  <<EMAIL>>

  * Replace std::ostream with LogStream.
  * Add LogFile and AsyncLogging.
  * Set SO_KEEPALIVE by default.
  * Add HighWaterMark callback to TcpConnection.
  * Add EventLoop::getEventLoopOfCurrentThread(),
    Add ThreadInitCallback to EventLoopThreadPool.
  * Add asio_chat_server_threaded_highperformance
  * Version 0.5.0

2012-05-18  Shuo Chen  <<EMAIL>>

  * Add FileUtil.
  * Add new functions in ProcessInfo
  * Add example for curl.
  * Add add RPC meta service proto.
  * Add loadtest for asio chat.
  * Version 0.3.5

2012-03-22  Shuo Chen  <<EMAIL>>

  * Add example for async rpc (resolver).
  * Install muduo_cdns
  * Version 0.3.4

2012-03-16  Shuo Chen  <<EMAIL>>

  * Remove net/protorpc2
    moved to http://github.com/chenshuo/muduo-protorpc
  * Install EventLoopThreadPool.h, rpc.proto and rpc.pb.h
  * Version 0.3.3

2012-03-11  Shuo Chen  <<EMAIL>>

  * Add asynchronous DNS stub resolver based on c-ares.
    See also https://github.com/chenshuo/muduo-udns
  * Replace string with StringPiece for function parameters.
  * Change default log level from DEBUG to INFO,
    set MUDUO_LOG_DEBUG=1 to revert.
  * Install Channel.h
  * Version 0.3.2

2012-03-01  Shuo Chen  <<EMAIL>>

  * Support multi-threaded http server.
  * Do not install SocketsOps.h
  * Version 0.3.1

2012-02-24  Shuo Chen  <<EMAIL>>

  * Support Keep-Alive for HTTP/1.0.
  * Check return value of pthread_create.
  * Minor fixes (set TcpNoDelay, stop() in ThreadPool::dtor)
  * Version 0.3.0

2011-09-18  Shuo Chen  <<EMAIL>>

  * EventLoop now supports cancelling timer.
  * Add two examples of asio chat server, demo copy-on-write
  in multithreaded program.
  * Version 0.2.9

2011-09-04  Shuo Chen  <<EMAIL>>

  * Refactored RPC implementation of version 1 and 2,
  programming interface differ, interoperable.
  version 2 is incomplete yet.
  * Find protobuf with cmake find_package().
  * Version 0.2.8

2011-09-03  Shuo Chen  <<EMAIL>>

  * Add a proof of concept implementation of Protobuf RPC.
  * Version 0.2.7

2011-06-27  Shuo Chen  <<EMAIL>>

  * Fix decoding of Sudoku request.
  * Backport to older Linux.
  * Add BoundedBlockingQueue
  * Version 0.2.6

2011-06-15  Shuo Chen  <<EMAIL>>

  * Add examples/sudoku.
  * Add thread benchmark.
  * Version 0.2.5

2011-06-02  Shuo Chen  <<EMAIL>>

  * Add examples/shorturl.
  * Version 0.2.4

2011-05-24  Shuo Chen  <<EMAIL>>

  * Fix warnings on Arch Linux (GCC 4.6.0), thanks to ifreedom
  * Add CMake install instructions, thanks to ifreedom
  * Fix warnings on 32-bit Linux, thanks to highshow
  * Version 0.2.3

2011-05-15  Shuo Chen  <<EMAIL>>

  * Changes from reactor tutorial
  * Version 0.2.2

2011-05-07  Shuo Chen  <<EMAIL>>

  * Try making TcpClient destructable
  * Add demux in examples/multiplexer
  * Add examples/socks4a
  * Changes for reactor tutorial
  * Version 0.2.1

2011-04-27  Shuo Chen  <<EMAIL>>

  * Add kick idle connection example in examples/idleconnection.
  * Add test harness to examples/multiplexer
  * Replace std::list with std::set in TimerQueue.
  * Version 0.2.0

2011-04-11  Shuo Chen  <<EMAIL>>

  * Add Google Protobuf codec and dispatcher
  * Revert 'Add max connection limit to simple echo example.'
  * Add max connection limit example in examples/maxconnection.
  * Version 0.1.9

2011-03-27  Shuo Chen  <<EMAIL>>

  * Add file transfer download examples.
  * Add max connection limit to simple echo example.
  * Make inputBuffer accessible in TcpConnection.
  * Const-ness correct in Buffer class.
  * Add Mutex test for benchmarking.
  * Replace anonymous namespace with muduo::detail in muduo/base.
  * Version 0.1.8

2011-02-03  Shuo Chen  <<EMAIL>>

  * Fix LengthHeaderCodec::onMessage() in examples/asio/chat.
  * Version 0.1.7

2011-02-01  Shuo Chen  <<EMAIL>>

  * Fix onConnection() in simple examples.
  * Reset t_cachedTid after fork().
  * Version 0.1.6

2010-12-15  Shuo Chen  <<EMAIL>>

  * Add examples/multiplexer
  * Fix epoll kNoneEvent
  * Version 0.1.5

2010-11-20  Shuo Chen  <<EMAIL>>

  * Fix retry logic
  * Version 0.1.4

2010-09-26  Shuo Chen  <<EMAIL>>

  * Check SO_ERROR when connection is made.

2010-09-11  Shuo Chen  <<EMAIL>>

  * Gracefully refuse clients when accept(2) returns EMFILE.
  * Version 0.1.3

2010-09-07  Shuo Chen  <<EMAIL>>

  * Libevent benchmark for event handling.
  * Version 0.1.2

2010-09-04  Shuo Chen  <<EMAIL>>

  * Ping-pong benchmark, version 0.1.1

2010-08-30  Shuo Chen  <<EMAIL>>

  * First pre-alpha release, version 0.1.0

2010-08-29  Shuo Chen  <<EMAIL>>

  * Sub works.

2010-08-28  Shuo Chen  <<EMAIL>>

  * Add twisted finger examples.

2010-08-27  Shuo Chen  <<EMAIL>>

  * Add simple chargen example.

2010-08-07  Shuo Chen  <<EMAIL>>

  * Add Date.

2010-05-15  Shuo Chen  <<EMAIL>>

  * Hub works.

2010-05-14  Shuo Chen  <<EMAIL>>

  * Inspects opened files and threads.

2010-05-11  Shuo Chen  <<EMAIL>>

  * Add inspector for process info.

2010-05-04  Shuo Chen  <<EMAIL>>

  * Add simple http server and client.

2010-04-25  Shuo Chen  <<EMAIL>>

  * Add examples.

2010-04-11  Shuo Chen  <<EMAIL>>

  * TcpClient works.

2010-04-03  Shuo Chen  <<EMAIL>>

  * TcpServer works.

2010-03-15  Shuo Chen  <<EMAIL>>

  * TcpConnection at server side works.

2010-03-14  Shuo Chen  <<EMAIL>>

  * Acceptor works.

2010-03-13  Shuo Chen  <<EMAIL>>

  * TimerQueue works.

2010-03-12  Shuo Chen  <<EMAIL>>

  * Starts working on Muduo.
